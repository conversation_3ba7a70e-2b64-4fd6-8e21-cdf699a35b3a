.sidenav {    position: fixed;
    right: -30px;
    top: 147px;
    width: 236px;
    height: auto;
    border-left: 1px solid #e0e6ed;}
.sidenav .sidenav-header:after { display: none; }
.sidenav .sidenav-header p {
    font-weight: 600;
    font-size: 18px;
    color: #fff;
    margin-bottom: 20px;
    background: #1b55e2;
    text-align: center;
    border-radius: 5px;
    padding: 4px;
    letter-spacing: 1px;
    background-image: linear-gradient(to right, #1b55e2 0%, #5c1ac3 100%);
}
.sidenav .sidenav-content {
    background-color: transparent;
    display: block;
    border: none;
}
.sidenav .sidenav-content a {
    display: block;
    padding: 3px 0px;
    color: #3b3f5c;
    font-size: 12px;
    padding: 3px 25px;
}
.sidenav .sidenav-content a.active {
    color: #5c1ac3;
    font-weight: 700;
    border-left: 1px solid #5c1ac3;
}
.sidenav .sidenav-content a:hover {
    color: #1b55e2;
    font-weight: 700;
    border-left: 1px solid #5c1ac3;
}
#content > .container {
    display: flex;
    max-width: 58.333333%!important;
    margin-left: 14px;
    padding: 0 32px!important;
}
#content > .container > .container { padding: 0; margin: 0; }
@media (max-width: 575px) {
    .sidenav .sidenav-content a {
        padding: 4px 7px;
        margin-right: 0;
        margin-bottom: 10px;
    }
}
@media (max-width: 991px) {
    #content > .container {
        padding: 0 16px!important;
    }
}
@media (max-width: 1199px) {
    .sidenav { display: none; }
    #content > .container {
        max-width: 100%!important;
        margin-left: auto;
    }
    #content > .container > .container {
        max-width: 100%;
    }
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .sidenav {
        display: none;
    }
}