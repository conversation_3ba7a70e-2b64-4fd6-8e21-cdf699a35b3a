<?php
session_start();
include ('../lib/db_connection.php');

$game_type = 1;
$game_id = get_wingo_gameid($game_type);

if (isset($_REQUEST['game_type']) && $_REQUEST['game_type'] != '') {
    $game_type = $_REQUEST['game_type'];
}

if (isset($_REQUEST['game_id']) && $_REQUEST['game_id'] != '') {
    $game_id = $_REQUEST['game_id'];
}

if (isset($_REQUEST['game_value']) && $_REQUEST['game_value'] != '') {
    $game_value = $_REQUEST['game_value'];
}

$manual_game = 'wingo_' . $game_type;

$result = dbQuery("UPDATE `tabl_manualresultswitch` SET switch='yes', `value`='$game_value' WHERE `game`='$manual_game'");

if ($result) {
    echo 1;
} else {
    echo 0;

}





?>