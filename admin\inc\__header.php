<?php
$p_pic = dbQuery("SELECT name,profile_image FROM tabl_admin WHERE id='" . $_SESSION['admin_id'] . "' AND admin_type='" . $_SESSION['admin_type'] . "'");
$res_p_pic = dbFetchAssoc($p_pic);
?>

<div class="header-container">
  <header class="header navbar navbar-expand-sm">

    <a href="javascript:void(0);" class="sidebarCollapse" data-placement="bottom"><svg
        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-menu">
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg></a>

    <div class="nav-logo align-self-center">
      <a class="navbar-brand" href="home.php"><span class="navbar-brand-name"><?php echo SITE; ?></span></a>
    </div>

    <ul class="navbar-item flex-row mr-auto">
      <li class="nav-item align-self-center search-animated">
        <form class="form-inline search-full form-inline search" role="search">
          <div class="search-bar">
            <input type="text" class="form-control search-form-control  ml-lg-auto" placeholder="Search...">
          </div>
        </form>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
          class="feather feather-search toggle-search">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
      </li>
    </ul>

    <ul class="navbar-item flex-row nav-dropdowns">

      <li class="nav-item dropdown user-profile-dropdown order-lg-0 order-1">
        <a href="javascript:void(0);" class="nav-link dropdown-toggle user" id="user-profile-dropdown"
          data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <div class="media">
            <img src="assets/img/thumb-50/<?php echo $res_p_pic['profile_image'] ?>" class="img-fluid"
              alt="admin-profile">
            <div class="media-body align-self-center">
              <h6><span>Hi,</span> <?php echo $res_p_pic['name']; ?></h6>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="feather feather-chevron-down">
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </a>
        <div class="dropdown-menu position-absolute animated fadeInUp" aria-labelledby="user-profile-dropdown">
          <div class="">
            <div class="dropdown-item">
              <a class="" href="my-account.php"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" class="feather feather-user">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg> My Profile</a>
            </div>

            <div class="dropdown-item">
              <a class="" href="setting.php">
                <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" class="feather feather-settings">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg> -->

                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256.001 256.001" id="gear-six">
                  <rect width="24" height="24" fill="none"></rect>
                  <path
                    d="M230.39551,109.76147a8.02782,8.02782,0,0,0-4.0044-5.60522L203.69775,91.55859h0a83.925,83.925,0,0,0-6.28759-10.90991l.437-25.96728a8.00126,8.00126,0,0,0-2.83594-6.25562,103.89316,103.89316,0,0,0-31.606-18.21777,8.02614,8.02614,0,0,0-6.85644.66528L134.292,44.22754h0a83.8907,83.8907,0,0,0-12.59179-.01l.02294.01392-22.293-13.376a8.00112,8.00112,0,0,0-6.83545-.67188,103.89444,103.89444,0,0,0-31.58008,18.2627A8.0265,8.0265,0,0,0,58.1626,54.7168l.437,25.95215h0a83.90687,83.90687,0,0,0-6.30469,10.8999L29.58984,104.1731a8.03308,8.03308,0,0,0-4.00732,5.61938,103.89,103.89,0,0,0,.03174,36.44605,8.02781,8.02781,0,0,0,4.00439,5.60522L52.3125,164.44141H52.312a83.92506,83.92506,0,0,0,6.2876,10.90991l.00049-.02661-.4375,25.99389a8.00126,8.00126,0,0,0,2.83594,6.25562,103.89311,103.89311,0,0,0,31.606,18.21777,8.026,8.026,0,0,0,6.85645-.66528l22.25683-13.35425h0a83.89083,83.89083,0,0,0,12.5918.01l-.023-.01392,22.293,13.376a8.00112,8.00112,0,0,0,6.83545.67188,103.89468,103.89468,0,0,0,31.58008-18.2627,8.02653,8.02653,0,0,0,2.85205-6.27051l-.437-25.95215h0a83.85174,83.85174,0,0,0,6.30468-10.8999l22.70508-12.60425a8.03312,8.03312,0,0,0,4.00733-5.61938A103.89047,103.89047,0,0,0,230.39551,109.76147ZM128.00488,176a48,48,0,1,1,48-48A47.99988,47.99988,0,0,1,128.00488,176Z"
                    opacity=".2"></path>
                  <path fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"
                    d="M52.29475,91.56894a83.91125,83.91125,0,0,1,6.30466-10.9001l0,0L58.1626,54.71694a8.02666,8.02666,0,0,1,2.852-6.27063A103.88918,103.88918,0,0,1,92.59485,30.18373a8.00115,8.00115,0,0,1,6.83547.67167l22.2927,13.376-.023-.01381a83.91075,83.91075,0,0,1,12.5921.00995v.00005l22.2566-13.3543a8.02664,8.02664,0,0,1,6.85653-.66537,103.88924,103.88924,0,0,1,31.606,18.218,8.00114,8.00114,0,0,1,2.836,6.25552l-.43756,25.994.00046-.02682a83.91052,83.91052,0,0,1,6.28743,10.91l0,0L226.391,104.15631a8.02663,8.02663,0,0,1,4.00449,5.60524,103.88977,103.88977,0,0,1,.03192,36.44593,8.03463,8.03463,0,0,1-4.00724,5.61943l-22.72861,12.61716.02346-.013a83.9124,83.9124,0,0,1-6.30466,10.9001l0,0,.43686,25.95193a8.02667,8.02667,0,0,1-2.852,6.27063,103.88913,103.88913,0,0,1-31.58021,18.26258,8.00115,8.00115,0,0,1-6.83547-.67167l-22.29271-13.376.023.01381a83.91061,83.91061,0,0,1-12.59209-.01v-.00005L99.46106,225.12674a8.02664,8.02664,0,0,1-6.85654.66538,103.88937,103.88937,0,0,1-31.606-18.218,8.00118,8.00118,0,0,1-2.836-6.25553l.43757-25.994-.00046.02682a83.91174,83.91174,0,0,1-6.28744-10.91005l.00005,0L29.61877,151.84369a8.02666,8.02666,0,0,1-4.0045-5.60525,103.89013,103.89013,0,0,1-.03192-36.44591,8.03467,8.03467,0,0,1,4.00725-5.61944L52.31821,91.55593Z">
                  </path>
                  <circle cx="128.005" cy="128" r="48" fill="none" stroke="#000" stroke-linecap="round"
                    stroke-linejoin="round" stroke-width="16"></circle>
                </svg>
                Setting</a>
            </div>
            <div class="dropdown-item">
              <a class="" href="amount_setting.php">
                <!-- <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="feather feather-money">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                </svg> -->



                <svg id="SvgjsSvg1011" width="24" height="24" xmlns="http://www.w3.org/2000/svg" version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.com/svgjs">
                  <defs id="SvgjsDefs1012"></defs>
                  <g id="SvgjsG1013"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24" height="24">
                      <path
                        d="M19.534 1026.494c.15 0 .299.046.44.137.28.18.474.536.476.935v.01a.5.5 0 0 0 .254.442h.002c.029.017.059.03.09.04h.002c.03.01.062.018.094.022H20.994c.016 0 .033 0 .05-.01.017 0 .033-.01.049-.012l.015-.01c.01 0 .02-.01.03-.01h.002c.015-.01.03-.012.045-.02a.485.485 0 0 0 .084-.057h.002a.512.512 0 0 0 .033-.031h.002a.505.505 0 0 0 .146-.36v-.01c.001-.097.011-.194.034-.285.068-.28.232-.514.443-.65a.78.78 0 0 1 .879 0c.28.18.476.536.478.936v.01a.5.5 0 0 0 .254.441h.002c.029.017.059.03.09.041h.002c.03.01.062.017.094.022H23.779c.034 0 .068 0 .101-.01.017 0 .033-.01.049-.012l.016-.01c.01 0 .02-.01.029-.01h.002a.487.487 0 0 0 .127-.074h.004a.512.512 0 0 0 .033-.03h.002a.5.5 0 0 0 .147-.36v-.01c.003-.4.196-.756.476-.936a.78.78 0 0 1 .879 0c.278.179.473.532.479.928 0 .01-.002.01-.002.016a.5.5 0 0 0 .443.504H26.615a.505.505 0 0 0 .181-.033h.024c.013-.01.025-.01.037-.016.015-.01.03-.016.043-.025a.5.5 0 0 0 .225-.424l-.002-.016c.005-.396.2-.75.478-.927a.78.78 0 0 1 .88 0c.275.176.466.525.474.918-.005 1.22-.492 2.392-1.373 3.318h-7.59c-.881-.926-1.368-2.099-1.373-3.318.008-.393.2-.742.476-.918.141-.09.29-.137.44-.137zm1.027 5.373h6.506v.89h-6.506zm-.23 1.89h6.91c6.625 5.539 8.332 10.553 8.332 14.288v.433c0 1.573-1.113 3.06-3.074 4.184-1.96 1.124-4.728 1.84-7.803 1.84h-1.82c-3.074 0-5.842-.716-7.803-1.84-1.96-1.124-3.072-2.61-3.072-4.184v-.433c0-3.735 1.705-8.75 8.33-14.287zm3.725 4.747c-3.59 0-6.483 2.919-6.483 6.498 0 3.58 2.893 6.5 6.483 6.5 3.588 0 6.517-2.917 6.517-6.5a.5.5 0 0 0-.039-.196h-.002c-.111-3.486-2.958-6.3-6.476-6.3zm0 1c3.048 0 5.517 2.459 5.517 5.498 0 3.04-2.469 5.5-5.517 5.5a5.474 5.474 0 0 1-5.483-5.5 5.473 5.473 0 0 1 5.483-5.498zm-2.442 2.027v.01h-.021v1h1.027c.934 0 1.405.441 1.574.99h-2.58v1h2.58c-.17.548-.64.99-1.574.99H21.593v.682l-.03.03.03.028v.26h.265l3.082 3.012.7-.715-2.415-2.358c1.097-.22 1.801-1.033 1.994-1.93h1.366v-1h-1.366c-.077-.359-.244-.7-.478-1h1.844v-1z"
                        color="#000" font-family="sans-serif" font-weight="400" overflow="visible"
                        transform="translate(-8 -1024.495)"
                        style="line-height:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000;text-transform:none;block-progression:tb;isolation:auto;mix-blend-mode:normal"
                        fill="#485359" class="color000 svgShape"></path>
                    </svg></g>
                </svg>

                Amount Setting</a>
            </div>
            <div class="dropdown-item">
              <a class="" href="logout.php"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" class="feather feather-log-out">
                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                  <polyline points="16 17 21 12 16 7"></polyline>
                  <line x1="21" y1="12" x2="9" y2="12"></line>
                </svg> Sign Out</a>
            </div>
          </div>
        </div>

      </li>
    </ul>
  </header>
</div>









<!--
<div class="air__layout__header">
        <div class="air__utils__header">
          <div class="air__topbar">
            <p class="mb-0 mr-4 d-xl-block d-none">
              Status
              <span class="ml-1 badge bg-danger text-white font-size-12 text-uppercase air__topbar__status"
                >LIVE</span
              >
            </p>
           
            <div class="dropdown">
              <a
                href="#"
                class="dropdown-toggle text-nowrap"
                data-toggle="dropdown"
                aria-expanded="false"
                data-offset="5,15"
                   >
                   
                   <?php
                   $p_pic = dbQuery("SELECT profile_image FROM tabl_admin WHERE id='" . $_SESSION['admin_id'] . "' AND admin_type='" . $_SESSION['admin_type'] . "'");
                   $res_p_pic = dbFetchAssoc($p_pic);

                   ?>
                <img class="dropdown-toggle-avatar" src="components/core/img/avatars/thumb-50/<?php echo $res_p_pic['profile_image'] ?>"   alt="User avatar" style="border-radius:25px;" />
              </a>
              <div class="dropdown-menu dropdown-menu-right" role="menu">
                <a class="dropdown-item" href="my-account.php">
                  <i class="dropdown-icon fe fe-user"></i>
                  Profile
                </a>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="logout.php">
                  <i class="dropdown-icon fe fe-log-out"></i> Logout
                </a>
              </div>
            </div>
       
          </div>
        </div>
      </div>-->