body {
    font-family: 'Quicksand', sans-serif;
    background-color: #f1f2f3;
}
.helpdesk:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: -1;
    pointer-events: none;
    height: 280px;
    background-color: #1b55e2;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 1000'%3E%3Cg %3E%3Ccircle fill='%232b50ed' cx='50' cy='0' r='50'/%3E%3Cg fill='%233154ea' %3E%3Ccircle cx='0' cy='50' r='50'/%3E%3Ccircle cx='100' cy='50' r='50'/%3E%3C/g%3E%3Ccircle fill='%233658e8' cx='50' cy='100' r='50'/%3E%3Cg fill='%233c5be5' %3E%3Ccircle cx='0' cy='150' r='50'/%3E%3Ccircle cx='100' cy='150' r='50'/%3E%3C/g%3E%3Ccircle fill='%23415fe2' cx='50' cy='200' r='50'/%3E%3Cg fill='%234662df' %3E%3Ccircle cx='0' cy='250' r='50'/%3E%3Ccircle cx='100' cy='250' r='50'/%3E%3C/g%3E%3Ccircle fill='%234b66dc' cx='50' cy='300' r='50'/%3E%3Cg fill='%235069d9' %3E%3Ccircle cx='0' cy='350' r='50'/%3E%3Ccircle cx='100' cy='350' r='50'/%3E%3C/g%3E%3Ccircle fill='%23546cd5' cx='50' cy='400' r='50'/%3E%3Cg fill='%23596fd2' %3E%3Ccircle cx='0' cy='450' r='50'/%3E%3Ccircle cx='100' cy='450' r='50'/%3E%3C/g%3E%3Ccircle fill='%235e72cf' cx='50' cy='500' r='50'/%3E%3Cg fill='%236275cb' %3E%3Ccircle cx='0' cy='550' r='50'/%3E%3Ccircle cx='100' cy='550' r='50'/%3E%3C/g%3E%3Ccircle fill='%236678c8' cx='50' cy='600' r='50'/%3E%3Cg fill='%236b7bc4' %3E%3Ccircle cx='0' cy='650' r='50'/%3E%3Ccircle cx='100' cy='650' r='50'/%3E%3C/g%3E%3Ccircle fill='%236f7ec0' cx='50' cy='700' r='50'/%3E%3Cg fill='%237381bc' %3E%3Ccircle cx='0' cy='750' r='50'/%3E%3Ccircle cx='100' cy='750' r='50'/%3E%3C/g%3E%3Ccircle fill='%237783b8' cx='50' cy='800' r='50'/%3E%3Cg fill='%237c86b4' %3E%3Ccircle cx='0' cy='850' r='50'/%3E%3Ccircle cx='100' cy='850' r='50'/%3E%3C/g%3E%3Ccircle fill='%238089b0' cx='50' cy='900' r='50'/%3E%3Cg fill='%23848bac' %3E%3Ccircle cx='0' cy='950' r='50'/%3E%3Ccircle cx='100' cy='950' r='50'/%3E%3C/g%3E%3Ccircle fill='%23888ea8' cx='50' cy='1000' r='50'/%3E%3C/g%3E%3C/svg%3E");
    background-attachment: fixed;
    background-size: contain;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .helpdesk:before {
        background-image: none;
    }
}
.helpdesk .navbar .navbar-brand {
    color: #fff;
    font-weight: 800;
    letter-spacing: 2px;
}
.helpdesk .navbar .nav-link { color: #fff!important; }

/*
	Helpdesk Header Wrapper
*/

.hd-header-wrapper {
    padding: 67px 0;
    border-radius: 4px;
}
.hd-header-wrapper h4 {
    color: #fff;
    font-size: 46px;
    padding: 0 8px;
}
.hd-header-wrapper p {
    color: #fff;
    font-size: 17px;
    font-weight: 600;
    letter-spacing: 1px;
    margin-bottom: 35px;
}
.hd-header-wrapper .input-group {
    -webkit-box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
    box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
}
.hd-header-wrapper span.input-group-text {
    background-color: #fff;
    border-color: #fff;
    padding: 11px 8px 11px 20px;
    border-radius: 4px;
    font-size: 22px;
    font-weight: normal !important;
}
.hd-header-wrapper span.input-group-text svg {
    font-weight: 600;
    color: #1b55e2;
}
.hd-header-wrapper .form-control:focus {
    box-shadow: none;
    border-color: #1b55e2;
    color: #3b3f5c;
    background: #f1f2f3;
}
.hd-header-wrapper input {
    border: none;
    border-radius: 4px;
    padding: 20px 16px;
    color: #0e1726;
    height: 100%;
}
.input-group .input-group-prepend .input-group-text {
    border: 1px solid #ffffff;
    background-color: #ffffff;
}

/*
	Tab Section
*/


/* 	Tab Content 	*/

.hd-tab-section .accordion .card {
    border: none;
    margin-bottom: 26px;
    -webkit-box-shadow: 2px 5px 17px 0 rgba(31,45,61,.1);
    box-shadow: 2px 5px 17px 0 rgba(31,45,61,.1);
    border-radius: 12px!important;
    cursor: pointer;
}
.hd-tab-section .accordion .card .card-header {
    background-color: #fff;
    color: #3b3f5c;
    border-color: transparent;
    padding: 0;
    border-radius: 6px;
}
.hd-tab-section .accordion .card .card-header div > div {
    padding: 13px 21px;
    cursor: pointer;
    background: transparent;
    border: none;
    overflow: hidden;
    white-space: nowrap;
    font-size: 13px;
    color: #3b3f5c;
    font-weight: 700;
}
.hd-tab-section .accordion .card .card-header div > div svg {
    width: 17px;
    vertical-align: middle;
    margin-right: 11px;
    color: #888ea8;
}
.hd-tab-section .accordion .card .card-header div > div[aria-expanded="true"] svg { color: #1b55e2; }
.hd-tab-section .accordion .card:hover .card-header div > div svg { color: #1b55e2; }
.hd-tab-section .accordion .card:not(:last-child) .card-header div > div { border-bottom: none; }
.hd-tab-section .accordion .card .card-body { padding: 20px 30px; }
.hd-tab-section .accordion .card .card-body p {
    line-height: 2.2;
    font-size: 14px;
    letter-spacing: 1px;
    color: #0e1726;
}

/*  Contact Content     */

.hd-contact-section { margin-bottom: 50px; }
.hd-contact-section .hd-slide-header {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 50px;
    color: #3b3f5c;
}
.carousel-item.community-help { padding: 13px; }
.carousel-item-content {
    max-width: 607px;
    max-width: 697px;
    margin: 0 auto;
    padding: 43px 0;
    height: 331px;
}

/*  Community help     */

.hd-contact-section .community-help .media {
    padding: 13px 25px;
    border: 1px solid #bfc9d4;
    border-radius: 12px;
    background: #fff;
}
.hd-contact-section .community-help .media svg {
    width: 54px;
    height: 55px;
    margin-right: 11px;
    align-self: center;
    color: #1b55e2;
    stroke-width: 1.5px;
}
.hd-contact-section .community-help .media h5 {
    color: #1b55e2;
    font-weight: 800;
     font-size: 17px; 
    margin-bottom: 5px;
}
.hd-contact-section .community-help .media p {
    font-size: 14px;
    font-weight: 600;
    color: #3b3f5c;
}

/*  News updates     */

.hd-contact-section .news-updates .media {
    padding: 13px 25px;
    border: 1px solid #bfc9d4;
    border-radius: 12px;
    background: #fff;
}
.hd-contact-section .news-updates .media svg {
    width: 54px;
    height: 55px;
    margin-right: 11px;
    color: #1b55e2;
    stroke-width: 1.5px;
}
.hd-contact-section .news-updates .media h5 {
    color: #1b55e2;
    font-weight: 800;
    margin-bottom: 0;
}
.hd-contact-section .news-updates .media p {
    font-size: 14px;
    font-weight: 600;
}


/*  Carousel indicators    */

.carousel-indicators {
    bottom: -34px;
    left: 0;
    right: 0;
    height: 22px;
}
.carousel-indicators li {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    opacity: 1;
    background-color: #888ea8;
}
.carousel-indicators li.active {
    background-color: #1b55e2;
}
.carousel-control-next, .carousel-control-prev {
    opacity: 1;
    background: #f1f2f3;
}
.carousel-control-prev-icon {
    background-image: none;
    -webkit-box-shadow: 2px 5px 17px 0 rgba(31,45,61,.1);
    box-shadow: 0px 0px 19px 3px rgba(31,45,61,.1);
    padding: 9px;
    height: 33px;
    width: 33px;
    position: relative;
    border-radius: 50px;
}
.carousel-control-prev-icon svg {
    color: #1b55e2;
    position: absolute;
    top: 4px;
    left: -1px;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    width: 23px;
}
.carousel-control-prev-icon:before { display: none; }
.carousel-control-next-icon {
    background-image: none;
    -webkit-box-shadow: 2px 5px 17px 0 rgba(31,45,61,.1);
    box-shadow: 0px 0px 19px 3px rgba(31,45,61,.1);
    padding: 9px;
    height: 33px;
    width: 33px;
    position: relative;
    border-radius: 50px;
}
.carousel-control-next-icon svg {
    color: #1b55e2;
    position: absolute;
    top: 4px;
    left: 0;
    right: -2px;
    margin-left: auto;
    margin-right: auto;
    width: 23px;
}
.carousel-control-next-icon:before { display: none; }


/*
    Mini Footer Wrapper
*/

#miniFooterWrapper {
    color: #fff;
    font-size: 14px;
    border-top: solid 1px #ffffff;
    padding: 14px;
    -webkit-box-shadow: 0px -1px 20px 0 rgba(31,45,61,.1);
    box-shadow: 0px -1px 20px 0 rgba(31,45,61,.1);
}
#miniFooterWrapper .arrow {
    background-color: #1b55e2;
    border-radius: 50%;
    position: absolute;
    z-index: 2;
    top: -33px;
    width: 40px;
    height: 40px;
    box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
    left: 0;
    right: 0;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    cursor: pointer;
}
#miniFooterWrapper .arrow p {
    align-self: center;
    margin-bottom: 0;
    color: #fff;
    font-weight: 600;
    font-size: 15px;
    letter-spacing: 1px;
}
#miniFooterWrapper .copyright a {
    color: #1b55e2;
    font-weight: 700;
    text-decoration: none;
}
@media (max-width: 1199px) {
    .carousel-control-next, .carousel-control-prev { background: transparent; }
}
@media (max-width: 991px) {
    .carousel-control-next, .carousel-control-prev { display: none; }
}

@media (max-width: 767px) {
    .hd-contact-section .community-help .media { margin-bottom: 3px; }
}
@media (max-width: 575px) {
    .carousel-item-content { height: auto; }
}