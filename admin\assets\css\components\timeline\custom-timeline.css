.mt-container { max-width: 570px; }
.modern-timeline {
    list-style: none;
    position: relative;
    padding: 50px 0 50px;
    margin: 0;
}
.modern-timeline:before {
    position: absolute;
    background: #ebedf2;
    bottom: 0;
    left: 50%;
    top: 0;
    content: "";
    width: 3px;
    margin-left: -1.5px;
}
.modern-timeline > li {
    margin-bottom: 50px;
    position: relative;
}
.modern-timeline > li:after, .modern-timeline > li:before {
    display: table;
    content: "";
}
.modern-timeline > li > .modern-timeline-badge {
    position: absolute;
    background: #fff;
    border: 3px solid #ebedf2;
    border-radius: 100%;
    height: 20px;
    width: 20px;
    margin-left: -10px;
    text-align: center;
    z-index: 1;
    left: 50%;
    top: 32px;
}
.modern-timeline > li > .modern-timeline-panel {
    position: relative;
    border: 1px solid #ebedf2;
    background: #fff;
    border-radius: .1875rem;
    box-shadow: 0 0 60px 0 rgba(0, 0, 0, .07);
    box-shadow: 0px 20px 20px rgba(126,142,177,0.12);
    transition: .3s ease-in-out;
    float: left;
    width: 46%;
    border-radius: 6px;
}
.modern-timeline > li > .modern-timeline-panel:before {
    position: absolute;
    background: #ebedf2;
    right: -37px;
    top: 40px;
    transition: .3s ease-in-out;
    content: " ";
    width: 37px;
    height: 3px;
    display: block;
}
.modern-timeline > li:nth-child(even) > .modern-timeline-panel:before {
    right: auto;
    left: -37px;
    width: 37px;
}
.modern-timeline > li:after { clear: both; }
.modern-timeline > li > .modern-timeline-panel .modern-timeline-preview img { width: 100%; border-top-left-radius: 6px; border-top-right-radius: 6px; }
.modern-timeline > li > .modern-timeline-panel *:last-child { margin-bottom: 0; }
.modern-timeline > li:nth-child(even) > .modern-timeline-panel {
    border: 1px solid #ebedf2;
    float: right;
}
.modern-timeline > li > .modern-timeline-panel *:last-child { margin-bottom: 0; }
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body { padding: 30px 20px; border-bottom-left-radius: 6px; border-bottom-right-radius: 6px; }
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body h4 {
    color: #e95f2b;
    margin-bottom: 20px;
    font-size: 1.125rem;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body p {
    color: #3b3f5c;
    margin-bottom: 0;
}
.modern-timeline > li > .modern-timeline-panel .modern-timeline-body p a { display: block; }
.modern-timeline > li > .modern-timeline-panel *:last-child { margin-bottom: 0; }
.modern-timeline-top:before, .modern-timeline-bottom:before {
    background: #ebedf2;
    position: absolute;
    height: 3px;
    width: 50px;
    display: block;
    content: "";
    bottom: 0;
    left: 50%;
    margin-left: -25px;
}
.modern-timeline-top:before { top: 0; }
@media (max-width: 767px) {
    ul.modern-timeline > li > .modern-timeline-panel {
        border: 1px solid #ebedf2;
        float: right;
        width: 100%;
    }
    ul.modern-timeline > li > .modern-timeline-badge, .modern-timeline > li > .modern-timeline-panel:before { display: none; }
}


/*
=====================
    Basic
=====================
*/
.timeline-line .item-timeline { display: flex; }
.timeline-line .item-timeline .t-dot { position: relative; }
.timeline-line .item-timeline .t-dot:before {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    border-color: #2196f3;
}
.timeline-line .item-timeline .t-dot:after {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    border-color: #2196f3;
    width: 0;
    height: auto;
    top: 25px;
    bottom: -15px;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
}
.timeline-line .item-timeline .t-dot.t-dot-primary:before { border-color: #1b55e2; }
.timeline-line .item-timeline .t-dot.t-dot-success:before { border-color: #8dbf42; }
.timeline-line .item-timeline .t-dot.t-dot-warning:before { border-color: #e2a03f; }
.timeline-line .item-timeline .t-dot.t-dot-info:before { border-color: #2196f3; }
.timeline-line .item-timeline .t-dot.t-dot-danger:before { border-color: #e7515a; }
.timeline-line .item-timeline .t-dot.t-dot-dark:before { border-color: #3b3f5c; }
.timeline-line .item-timeline .t-dot.t-dot-primary:after { border-color: #1b55e2; }
.timeline-line .item-timeline .t-dot.t-dot-success:after { border-color: #8dbf42; }
.timeline-line .item-timeline .t-dot.t-dot-warning:after { border-color: #e2a03f; }
.timeline-line .item-timeline .t-dot.t-dot-info:after { border-color: #2196f3; }
.timeline-line .item-timeline .t-dot.t-dot-danger:after { border-color: #e7515a; }
.timeline-line .item-timeline .t-dot.t-dot-dark:after { border-color: #3b3f5c; }
.timeline-line .item-timeline:last-child .t-dot:after { display: none; }
.timeline-line .item-timeline .t-meta-time {
    margin: 0;
    min-width: 100px;
    max-width: 100px;
    font-size: 12px;
    font-weight: 700;
    color: #888ea8;
    align-self: center;
}
.timeline-line .item-timeline .t-text {
    padding: 10px;
    align-self: center;
        margin-left: 10px;
}
.timeline-line .item-timeline .t-text p {
    font-size: 13px;
    margin: 0;
    color: #3b3f5c;
    font-weight: 600;
}
.timeline-line .item-timeline .t-text p a {
    color: #1b55e2;
    font-weight: 600;
}
.timeline-line .item-timeline .t-time {
    margin: 0;
    min-width: 58px;
    max-width: 100px;
    font-size: 16px;
    font-weight: 600;
    color: #3b3f5c;
    padding: 10px 0;
}
.timeline-line .item-timeline .t-text .t-meta-time {
    margin: 0;
    min-width: 100px;
    max-width: 100px;
    font-size: 12px;
    font-weight: 700;
    color: #888ea8;
    align-self: center;
}

/*
=====================
    Modern
=====================
*/
.timeline-alter .item-timeline { display: flex; }
.timeline-alter .item-timeline .t-time {
    padding: 10px;
    align-self: center;
}
.timeline-alter .item-timeline .t-time p {
    margin: 0;
    min-width: 58px;
    max-width: 100px;
    font-size: 16px;
    font-weight: 600;
    color: #3b3f5c;
    align-self: center;
}
.timeline-alter .item-timeline .t-img {
    position: relative;
    border-color: #ebedf2;
    padding: 10px;
}
.timeline-alter .item-timeline .t-img:before {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
}
.timeline-alter .item-timeline .t-img:after {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: auto;
    top: 25px;
    bottom: -15px;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
}
.timeline-alter .item-timeline .t-img img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    z-index: 7;
    position: relative;
}
.timeline-alter .item-timeline .t-usr-txt {
    display: block;
    padding: 10px;
    position: relative;
    border-color: #ebedf2;
}
.timeline-alter .item-timeline .t-usr-txt:before {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
}
.timeline-alter .item-timeline .t-usr-txt:after {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: auto;
    top: 25px;
    bottom: -15px;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
}
.timeline-alter .item-timeline .t-usr-txt p {
    margin: 0;
    background: #c2d5ff;
    height: 45px;
    width: 45px;
    border-radius: 50%;
    display: flex;
    align-self: center;
    justify-content: center;
    margin-bottom: 0;
    color: #1b55e2;
    font-weight: 700;
    font-size: 18px;
    z-index: 7;
    position: relative;
}
.timeline-alter .item-timeline .t-usr-txt span { align-self: center; }
.timeline-alter .item-timeline .t-meta-time {
    padding: 10px;
    align-self: center;
}
.timeline-alter .item-timeline .t-meta-time p {
    margin: 0;
    min-width: 100px;
    max-width: 100px;
    font-size: 12px;
    font-weight: 700;
    color: #888ea8;
}
.timeline-alter .item-timeline .t-text {
    padding: 10px;
    align-self: center;
}
.timeline-alter .item-timeline .t-text p {
    font-size: 13px;
    margin: 0;
    color: #3b3f5c;
    font-weight: 600;
}
.timeline-alter .item-timeline .t-text p a {
    color: #1b55e2;
    font-weight: 600;
}


/*
=======================
    Timeline Simple
=======================
*/

.timeline-simple {
    margin-bottom: 45px;
    max-width: 1140px;
    margin-right: auto;
    margin-left: auto;
}
.timeline-simple h3 {
    font-size: 23px;
    font-weight: 600;
}
.timeline-simple p.timeline-title {
    position: relative;
    font-size: 19px;
    font-weight: 600;
    color: #1b55e2;
    margin-bottom: 28px;
}
.timeline-simple p.timeline-title:before {
    position: absolute;
    content: "";
    height: 2px;
    width: 70px;
    background: #1b55e2;
    border-radius: 50px;
    bottom: -2px;
    left: 15px;
}
.timeline-simple .timeline-list p.meta-update-day {
    margin-bottom: 24px;
    font-size: 16px;
    font-weight: 600;
    color: #888ea8;
}
.timeline-simple .timeline-list .timeline-post-content { display: flex; }
.timeline-simple .timeline-list .timeline-post-content > div > div { margin-top: 28px; }
.timeline-simple .timeline-list .timeline-post-content:not(:last-child) > div > div { margin-bottom: 70px; }
.timeline-simple .timeline-list .timeline-post-content div.user-profile {
    position: relative;
    z-index: 2;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile:after {
    content: '';
    position: absolute;
    border-color: inherit;
    border-width: 2px;
    border-style: solid;
    top: 15px;
    left: 34%;
    transform: translateX(-50%);
    width: 0;
    height: auto;
    top: 48px;
    bottom: -15px;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
    z-index: -1;
    border-color: #ebedf2;
}
.timeline-simple .timeline-list .timeline-post-content div.user-profile img {
    width: 53px;
    height: 53px;
    border-radius: 50%;
    margin-right: 30px;
    -webkit-box-shadow: 0px 4px 9px 0px rgba(31, 45, 61, 0.31);
    box-shadow: 0px 4px 9px 0px rgba(31, 45, 61, 0.31);
}
.timeline-simple .timeline-list .timeline-post-content h4 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 0;
    color: #1b55e2;
}
.timeline-simple .timeline-list .timeline-post-content .meta-time-date { }
.timeline-simple .timeline-list .timeline-post-content svg {
    color: #888ea8;
    vertical-align: text-bottom;
    width: 21px;
    height: 21px;
}
.timeline-simple .timeline-list .timeline-post-content:hover svg {
    color: #1b55e2;
    fill: rgba(27, 85, 226, 0.23921568627450981);
}
.timeline-simple .timeline-list .timeline-post-content svg.feather-calendar {  }
.timeline-simple .timeline-list .timeline-post-content h6 {
    display: inline-block;
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 11px;
}
.timeline-simple .timeline-list .timeline-post-content:hover h6 { color: #888ea8; }
.timeline-simple .timeline-list .timeline-post-content p.post-text {
    padding-left: 31px;
    color: #888ea8;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 28px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers {
    padding-left: 31px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers img {
    width: 38px;
    border-radius: 50%;
    margin-right: 7px;
    -webkit-box-shadow: 0px 6px 9px 2px rgba(31, 45, 61, 0.31);
    box-shadow: 1px 3px 7px 2px rgba(31, 45, 61, 0.31);
    -webkit-transition: all 0.35s ease;
    transition: all 0.35s ease;
    cursor: pointer;
    margin-bottom: 5px;
}
.timeline-simple .timeline-list .timeline-post-content .post-contributers img:hover {
    -webkit-transform: translateY(-3px) scale(1.02);
    transform: translateY(-3px) scale(1.02);
    box-shadow: none;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img {
    padding-left: 31px;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img {
    width: 20%;
    border-radius: 6px;
    -webkit-box-shadow: 0px 6px 9px 2px rgba(31, 45, 61, 0.31);
    box-shadow: 1px 3px 7px 2px rgba(31, 45, 61, 0.31);
    -webkit-transition: all 0.35s ease;
    transition: all 0.35s ease;
    cursor: pointer;
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img:hover {
    -webkit-transform: translateY(-3px) scale(1.02);
    transform: translateY(-3px) scale(1.02);
    box-shadow: none;    
}
.timeline-simple .timeline-list .timeline-post-content .post-gallery-img img:not(:last-child) {
    margin-right: 23px;
}
@media (max-width: 767px) {
    .timeline-simple .timeline-list .timeline-post-content .post-gallery-img img {
        width: 150px;
        margin-bottom: 23px;
    }
}
@media (max-width: 575px) {
    .timeline-alter .item-timeline {
        display: block; 
        text-align: center;
    }
    .timeline-alter .item-timeline .t-meta-time p { margin: 0 auto; }
    .timeline-alter .item-timeline .t-usr-txt p { margin: 0 auto; }
    .timeline-simple .timeline-list .timeline-post-content { display: block; }
    .timeline-simple .timeline-list .timeline-post-content div.user-profile:after { display: none; }
    .timeline-simple .timeline-list .timeline-post-content div.user-profile {
        margin-bottom: 18px;
        text-align: center;
    }
    .timeline-simple .timeline-list .timeline-post-content div.user-profile img { margin-right: 0; }
    .timeline-simple .timeline-list .timeline-post-content h4 { text-align: center; }
    .timeline-simple .timeline-list .timeline-post-content .meta-time-date { text-align: center; }
}