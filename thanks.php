<?php
include('./lib/auth.php');
// session_start();
// include ('admin/lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


if (empty($_SESSION['razorpay_order_id'])) {
    echo '<script>window.location.href="deposit.php";</script>';
}

$sel = dbQuery("SELECT * FROM tabl_deposits WHERE ref_num='" . $_SESSION['razorpay_order_id'] . "'");
$res = dbFetchAssoc($sel);

unset($_SESSION['razorpay_order_id']);

if (isset($_SESSION['amount'])) {
    unset($_SESSION['amount']);
}

if (isset($_SESSION['deposit_id'])) {
    unset($_SESSION['deposit_id']);
}

?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Show Baazi - Recharge Success</title>

    <link href="https://fonts.googleapis.com/css?family=Nunito+Sans:400,400i,700,900&display=swap" rel="stylesheet" />

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
        integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">

    <!-- Google Tag Manager -->
    <script>
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-WX92GKXD');
    </script>
    <!-- End Google.Tag.Manager.-->


</head>
<style>
    body {
        text-align: center;
        padding: 40px 0;
        background: #ebf0f5;
    }

    h1 {
        color: #88b04b;
        font-family: "Nunito Sans", "Helvetica Neue", sans-serif;
        font-weight: 900;
        font-size: 40px;
        margin-bottom: 10px;
    }

    p {
        color: #404f5e;
        font-family: "Nunito Sans", "Helvetica Neue", sans-serif;
        font-size: 20px;
        margin: 0;
    }

    i {
        color: #9abc66;
        font-size: 100px;
        line-height: 200px;
        margin-left: -15px;
    }

    .card {
        background: white;
        padding: 60px;
        border-radius: 4px;
        box-shadow: 0 2px 3px #c8d0d8;
        display: inline-block;
        margin: 0 auto;
    }
</style>

<body>
    <div class="card">
        <div style="border-radius: 200px;height: 200px;width: 200px;background: #f8faf5;margin: 0 auto;">
            <i class="checkmark">✓</i>
        </div>
        <h1>Success</h1>
        <p>
            Your recharge successfully done!<br />
        </p>

        <a href="index.php" class="btn btn-success mt-3">Back to home</a>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"
        integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct"
        crossorigin="anonymous"></script>

    <script>
        // URL to redirect to
        var url = "https://showbaazi.com/games/";

        // Function to redirect after 5 seconds
        setTimeout(function() {
            window.location.href = url;
        }, 5000); // 5000 milliseconds = 5 seconds
    </script>
</body>

</html>