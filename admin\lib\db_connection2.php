<?php
define ('HOSTNAME', 'localhost');

define ('USERNAME', 'abclotto_db1');
define ('PASSWORD', 'abclotto_db1');
define ('DATABASE_NAME', 'abclotto_db1');

// define ('USERNAME', 'root');
// define ('PASSWORD', '');
// define ('DATABASE_NAME', 'kri_abclotto');


$con = mysqli_connect(HOSTNAME,USERNAME,PASSWORD,DATABASE_NAME);
if (mysqli_connect_errno())
  {
  echo "Failed to connect to MySQL: " . mysqli_connect_error();
  }
 

function dbQuery($sql)
{
	global $con;
	
	$result = mysqli_query($con, $sql);
	
	return $result;
}

function dbAffectedRows()
{
	global $con;
	
	return mysqli_affected_rows($con);
}

// function dbFetchArray($result, $resultType = MYSQL_NUM) {
// 	return mysqli_fetch_array($result, $resultType);
// }

function dbFetchAssoc($result)
{
	return mysqli_fetch_assoc($result);
}

function dbFetchRow($result) 
{
	return mysqli_fetch_row($result);
}

function dbFreeResult($result)
{
	return mysqli_free_result($result);
}

function dbNumRows($result)
{
	return mysqli_num_rows($result);
}

// function dbSelect($con)
// {
// 	return mysqli_select_db($con);
// }

function dbInsertId()
{
	global $con;
	return mysqli_insert_id($con);
}

$setting=dbQuery("SELECT * FROM tabl_setting WHERE id=1");
$res_setting=dbFetchAssoc($setting);

define("SITE", $res_setting['site_name']);
define("EMAIL",$res_setting['site_email']);

?>