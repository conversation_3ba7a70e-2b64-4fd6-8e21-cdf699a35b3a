<?php
session_start();
include('../lib/db_connection.php');
$user = $_REQUEST['username'];
$password = $_REQUEST['password'];

$query = dbQuery("SELECT * FROM  `tabl_admin` WHERE  `user` =  '" . $user . "' AND PASSWORD =  '" . $password . "' AND admin_type='1'");

$num_rows = dbNumRows($query);
if ($num_rows > 0) {
    $result = dbFetchAssoc($query);

    $_SESSION["user"] = $result['user'];
    $_SESSION["name"] = $result['name'];
    $_SESSION["admin_id"] = $result['id'];
    $_SESSION["admin_type"] = $result['admin_type'];

    echo '1';
} else {

    echo '2';
}
