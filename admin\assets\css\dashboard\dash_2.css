.layout-spacing {
    padding-bottom: 25px;
}
.layout-top-spacing {
    margin-top: 15px;
}
.widget {
    position: relative;
    padding: 0;
    border-radius: 6px;
    border: none;
    border: 1px solid #e0e6ed;
    box-shadow: 0 0 40px 0 rgba(94,92,154,.06);
}
.footer-wrapper {
    padding: 10px 0 10px 0;
}

/*
    ==================
        Notification
    ==================
*/
.widget-card-four {
    padding: 25px 32px;
    background: #fff;
}
.widget-card-four .w-content {
    display: flex;
    justify-content: space-between;
}

.widget-card-four .w-info h6 {
    font-weight: 600;
    margin-bottom: 0;
    color: #0e1726;
    font-size: 23px;
    letter-spacing: 0;
}
.widget-card-four .w-info p {
    font-weight: 600;
    margin-bottom: 0;
    color: #5c1ac3;
    font-size: 16px;
}
.widget-card-four .w-icon {
    color: #5c1ac3;
    background-color: #dccff7;
    height: 45px;
    display: inline-flex;
    width: 45px;
    align-self: center;
    justify-content: center;
    border-radius: 50%;
    padding: 10px;
}
.widget-card-four .progress {
    height: 8px;
    margin-bottom: 0;
    margin-top: 62px;
    margin-bottom: 0;
    height: 22px;
    padding: 4px;
    border-radius: 20px;
    box-shadow: 0 2px 2px rgba(224, 230, 237, 0.****************),
}
.widget-card-four .progress-bar {
    position: relative;
}
.widget-card-four .progress-bar:before {
    content: '';
    height: 7px;
    width: 7px;
    background: #fff;
    position: absolute;
    right: 3px;
    border-radius: 50%;
    top: 3.4px;
}

/*
    =====================
        User Analytics
    =====================
*/

.widget.widget-one {
    padding: 22px 18px;
    background: #fff;
}
.widget.widget-one .widget-heading h6 {
    color: #0e1726;
    margin-bottom: 41px;
    font-size: 17px;
    display: block;
    font-weight: 600;
}
.widget.widget-one .w-chart {
    display: flex;
}
.widget.widget-one .w-chart .w-chart-section {
    width: 50%;
    padding: 0 12px;
}
.widget.widget-one .w-chart .w-chart-section .w-detail {
    position: absolute;
    color: #fff;
}
.widget.widget-one .w-chart .w-chart-section .w-title {
    font-size: 13px;
    font-weight: 700;
    margin-bottom: 0;
}
.widget.widget-one .w-chart .w-chart-section .w-stats {
    color: #f8538d;
    font-size: 20px;
    letter-spacing: 1px;
    margin-bottom: 0;
    font-weight: 700;
}

/*
    =====================
        Unique Visitors
    =====================
*/

.widget.widget-chart-three {
    background: #fff;
}
.widget.widget-chart-three .widget-heading {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px dashed #e0e6ed;
    padding: 20px 20px;
    margin-bottom: 0;
    padding-bottom: 20px;
}
.widget.widget-chart-three .widget-heading h5 {
    font-size: 17px;
    display: block;
    color: #0e1726;
    font-weight: 600;
    margin-bottom: 0;
}
.widget.widget-chart-three .widget-heading .dropdown {
    align-self: center;
}
.widget.widget-chart-three .widget-heading .dropdown a svg {
    color: #888ea8;
    width: 19px;
    height: 19px;
}
.widget.widget-chart-three .widget-heading .dropdown .dropdown-menu {
    padding: 8px 8px;
    min-width: 10rem;
    border-radius: 6px;
    top: 5px !important;
}
.widget.widget-chart-three .apexcharts-legend-marker {
    left: -5px!important;
}

/*
    =========================
        Organic Vs Direct
    =========================
*/

/*
    ========================
        Recent Activities
    ========================
*/

.widget.widget-activity-three {
    position: relative;
    background: #fff;
    border-radius: 8px;
    height: 100%;
}
.widget.widget-activity-three .widget-heading {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px dashed #e0e6ed;
    padding: 20px 20px;
    padding-bottom: 20px;
}
.widget.widget-activity-three .widget-heading h5 {
    font-size: 17px;
    display: block;
    color: #0e1726;
    font-weight: 600;
    margin-bottom: 0;
}
.widget-activity-three .widget-content {
    padding: 20px 10px 20px 20px
}
.widget-activity-three .mt-container {
    position: relative;
    height: 325px;
    overflow: auto;
    padding: 0 12px 0 12px;
}
.widget-activity-three .timeline-line .item-timeline { display: flex;
    margin-bottom: 20px; }
.widget-activity-three .timeline-line .item-timeline .t-dot { position: relative; }

.widget-activity-three .timeline-line .item-timeline .t-dot div {
    background: #1b55e2;
    border-radius: 50%;
    padding: 5px;
    margin-right: 11px;
    display: flex;
    height: 37px;
    justify-content: center;
    width: 36px;
}
.widget-activity-three .timeline-line .item-timeline .t-dot div.t-primary {
    background-color: #1b55e2;
    box-shadow: 0 10px 20px -10px #1b55e2;
}
.widget-activity-three .timeline-line .item-timeline .t-dot div.t-success {
    background-color: #009688;
    box-shadow: 0 10px 20px -10px #009688;
}
.widget-activity-three .timeline-line .item-timeline .t-dot div.t-danger {
    background-color: #e7515a;
    box-shadow: 0 10px 20px -10px #e7515a;
}
.widget-activity-three .timeline-line .item-timeline .t-dot div.t-warning {
    background-color: #e2a03f;
    box-shadow: 0 10px 20px -10px #e2a03f;
}
.widget-activity-three .timeline-line .item-timeline .t-dot div.t-dark {
    background-color: #3b3f5c;
    box-shadow: 0 10px 20px -10px #3b3f5c;
}
.widget-activity-three .timeline-line .item-timeline .t-dot svg {
    color: #fff;
    height: 20px;
    width: 20px;
    stroke-width: 1.6px;
    align-self: center;
}

.widget-activity-three .timeline-line .item-timeline .t-content {
    width: 100%;
}
.widget-activity-three .timeline-line .item-timeline .t-content .t-uppercontent {
    display: flex;
    justify-content: space-between;
}
.widget-activity-three .timeline-line .item-timeline .t-content .t-uppercontent h5 {
    font-size: 15px;
    letter-spacing: 0;
    font-weight: 700;
    margin-bottom: 5px;
}
.widget-activity-three .timeline-line .item-timeline .t-content .t-uppercontent span {
    margin-bottom: 0;
    font-size: 11px;
    font-weight: 500;
    color: #888ea8;
}
.widget-activity-three .timeline-line .item-timeline .t-content p {
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: #888ea8;
}
.widget-activity-three .timeline-line .item-timeline .t-content div.tags {
    
}
.widget-activity-three .timeline-line .item-timeline .t-content div.tags .badge {
    padding: 2px 4px;
    font-size: 11px;
    letter-spacing: 1px;
    transform: none;
}
.widget-activity-three .timeline-line .item-timeline .t-content div.tags .badge-primary {
    background-color: #c2d5ff;
    color: #1b55e2;
    
}

.widget-activity-three .timeline-line .item-timeline .t-content div.tags .badge-success {
    background-color: #e6ffbf;
    color: #009688;
}

.widget-activity-three .timeline-line .item-timeline .t-content div.tags .badge-warning {
    background-color: #ffeccb;
    color: #e2a03f;
    
}
.widget-activity-three .timeline-line .item-timeline .t-dot:after {
    content: '';
    position: absolute;
    border-width: 1px;
    border-style: solid;
    left: 40%;
    transform: translateX(-50%);
    border-color: #bfc9d4;
    width: 0;
    height: auto;
    top: 36px;
    bottom: -20px;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    border-radius: 0;
}
.widget-activity-three .timeline-line .item-timeline:last-child .t-dot:after { display: none; }

/*
    ==================
        Statistics
    ==================
*/

.widget-one_hybrid {
    background: #fff;
}
.widget-one_hybrid .widget-heading {
    padding: 20px 13px;
}
.widget-one_hybrid .widget-heading .w-icon {
    display: inline-block;
    padding: 7px 8px;
    border-radius: 50%;
    margin-bottom: 15px;
}
.widget-one_hybrid .widget-heading svg {
    height: 18px;
    width: 18px;
}
.widget-one_hybrid .widget-heading .w-value {
    font-size: 21px;
    font-weight: 700;
    margin-bottom: 0;
}
.widget-one_hybrid .widget-heading h5 {
    margin-bottom: 0;
    font-size: 15px;
    font-weight: 600;
    color: #506690;
}
.widget-one_hybrid.widget-followers {
    background: #c2d5ff;
}
.widget-one_hybrid.widget-followers .widget-heading .w-icon {
    color: #1b55e2;
    border: 1px solid #1b55e2;
}
.widget-one_hybrid.widget-referral {
    background: #ffe1e2;
}
.widget-one_hybrid.widget-referral .widget-heading .w-icon {
    color: #e7515a;
    border: 1px solid #e7515a;
}
.widget-one_hybrid.widget-social {
    background: #bae7ff;
}
.widget-one_hybrid.widget-social .widget-heading .w-icon {
    color: #2196f3;
    border: 1px solid #2196f3;
}
.widget-one_hybrid.widget-engagement {
    background: #e6ffbf;
}
.widget-one_hybrid.widget-engagement .widget-heading .w-icon {
    color: #8dbf42;
    border: 1px solid #8dbf42;
}

/*
    ==================
        Balance
    ==================
*/

.widget-account-invoice-two {
    padding: 22px 19px;
    background: #e2a03f;
    background: linear-gradient(to right, #6b7764 0%, #2f384f 100%);
}
.widget-account-invoice-two .account-box .info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 84px;
}
.widget-account-invoice-two .account-box h5 {
    color: #e0e6ed;
    margin-bottom: 0;
    font-size: 17px;
    font-weight: 600;
}
.widget-account-invoice-two .account-box p {
    color: #e0e6ed;
    font-weight: 700;
    margin-bottom: 0;
    align-self: center;
}
.widget-account-invoice-two .account-box .acc-action {
    margin-top: 23px;
    display: flex;
    justify-content: space-between;
}
.widget-account-invoice-two .account-box .acc-action a {
    display: inline-block;
    padding: 8px;
    border-radius: 6px;
    color: #e0e6ed;
    font-weight: 600;
    box-shadow: 0px 0px 2px 0px #bfc9d4;
}
.widget-account-invoice-two .account-box .acc-action a:first-child {
    margin-right: 2px;
}
.widget-account-invoice-two .account-box .acc-action a svg {
    width: 18px;
    height: 18px;
}

/*
    ==================
        Statistics
    ==================
*/
.widget-card-one {
    background: #fff;
    padding: 20px 0;
    height: 100%;
}
.widget-card-one .widget-content .media {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 19px;
    padding-bottom: 21px;
    border-bottom: 1px dashed #e0e6ed;
}
.widget-card-one .widget-content .media .w-img {
    margin-right: 9px;
    align-self: center;
}
.widget-card-one .widget-content .media img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #e0e6ed;
}
.widget-card-one .widget-content .media-body {
    align-self: center;
}
.widget-card-one .widget-content .media-body h6 {
    font-weight: 700;
    font-size: 15px;
    letter-spacing: 0;
    margin-bottom: 0;
}
.widget-card-one .widget-content .media-body p {
    font-size: 14px;
    letter-spacing: 1px;
    margin-bottom: 0;
    font-weight: 600;
    color: #888ea8;
    padding: 0;
}
.widget-card-one .widget-content p {
    font-weight: 600;
    font-size: 13px;
    margin-bottom: 61px;
    padding: 0 20px;
}
.widget-card-one .widget-content .w-action {
    padding: 0 20px;
}
.widget-card-one .widget-content .w-action svg {
    color: #1b55e2;
    fill: #c2d5ff;
}
.widget-card-one .widget-content .w-action span {
    vertical-align: sub;
    font-weight: 700;
    color: #0e1726;
    letter-spacing: 1px;
}

/*
    ====================
        Visitors by Browser
    ====================
*/

.widget-four {
    position: relative;
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    height: 100%;
    border: 1px solid #e0e6ed;
    box-shadow: 0 0 40px 0 rgba(94,92,154,.06);
}
.widget-four .widget-heading {
    margin-bottom: 54px;
}
.widget-four .widget-heading h5 {
    font-size: 17px;
    display: block;
    color: #0e1726;
    font-weight: 600;
    margin-bottom: 0;
}
.widget-four .widget-content {
    font-size: 17px;
}
.widget-four .widget-content .browser-list {
    display: flex;
}
.widget-four .widget-content .browser-list:not(:last-child) {
    margin-bottom: 30px;
}
.widget-four .widget-content .w-icon {
    display: inline-block;
    padding: 8px 8px;
    border-radius: 50%;
    display: inline-flex;
    align-self: center;
    height: 34px;
    width: 34px;
    margin-right: 12px;
}
.widget-four .widget-content .w-icon svg {
    display: block;
    width: 17px;
    height: 17px;
}
.widget-four .widget-content .browser-list:nth-child(1) .w-icon {
    background: #c2d5ff;
}
.widget-four .widget-content .browser-list:nth-child(2) .w-icon {
    background: #ffe1e2;
}
.widget-four .widget-content .browser-list:nth-child(3) .w-icon {
    background: #ffeccb ;
}
.widget-four .widget-content .browser-list:nth-child(1) .w-icon svg {
    color: #1b55e2;
}
.widget-four .widget-content .browser-list:nth-child(2) .w-icon svg {
    color: #e7515a;
}
.widget-four .widget-content .browser-list:nth-child(3) .w-icon svg {
    color: #e2a03f;
}
.widget-four .widget-content .w-browser-details {
    width: 100%;
    align-self: center;
}
.widget-four .widget-content .w-browser-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1px;
}
.widget-four .widget-content .w-browser-info h6 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 0;
    color: #888ea8;
}
.widget-four .widget-content .w-browser-info p {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 0;
    color: #888ea8;
}
.widget-four .widget-content .w-browser-stats .progress {
    margin-bottom: 0;
    height: 22px;
    padding: 4px;
    border-radius: 20px;
    box-shadow: 0 2px 2px rgba(224, 230, 237, 0.****************), 1px 6px 7px rgba(224, 230, 237, 0.****************);
}
.widget-four .widget-content .w-browser-stats .progress .progress-bar {
    position: relative;
}
.widget-four .widget-content .w-browser-stats .progress .progress-bar:before {
    content: '';
    height: 7px;
    width: 7px;
    background: #fff;
    position: absolute;
    right: 3px;
    border-radius: 50%;
    top: 3.4px;
}
/*
    ==================
        Dev Summit
    ==================
*/
.widget-card-two {
    background: #fff;
    padding: 20px 0px;
}
.widget-card-two .media {
    padding-left: 15px;
    padding-right: 15px;
    margin-bottom: 19px;
    padding-bottom: 21px;
    border-bottom: 1px dashed #e0e6ed;
}
.widget-card-two .media .w-img {
    margin-right: 10px;
}
.widget-card-two .media .w-img img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #e0e6ed;
}
.widget-card-two .media .media-body {
    align-self: center;
}
.widget-card-two .media .media-body h6 {
    font-weight: 700;
    font-size: 15px;
    letter-spacing: 0;
    margin-bottom: 0;
}
.widget-card-two .media .media-body p {
    margin-bottom: 0;
    font-weight: 600;
    color: #888ea8;
}
.widget-card-two .card-bottom-section {
    text-align: center;
}
.widget-card-two .card-bottom-section h5 {
    font-size: 14px;
    color: #009688;
    font-weight: 700;
    margin-bottom: 20px;
}
.widget-card-two .card-bottom-section .img-group img {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    border: 2px solid #e0e6ed;
}
.widget-card-two .card-bottom-section .img-group img:not(:last-child) {
    margin-right: 5px;    
}
.widget-card-two .card-bottom-section a {
    display: block;
    margin-top: 18px;
    background: #1b55e2;
    color: #fff;
    padding: 10px 10px;
    transform: none;
    margin-right: 15px;
    margin-left: 15px;
    font-size: 15px;
    font-weight: 600;
    letter-spacing: 1px;
}
.widget-card-two .card-bottom-section a.btn:hover,
.widget-card-two .card-bottom-section a.btn:focus  {
    border-color: #1b55e2;
}

/*
    =====================
        Task Indicator
    =====================
*/
.widget-five {
    background: #fff;
    padding: 28px 0 0 0;
    height: 100%;
}
.widget-five .widget-content .header {
    display: flex;
    justify-content: space-between;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 23px;
    border-bottom: 1px dashed #e0e6ed;
}
.widget-five .widget-content .header-body {
    align-self: center;
}
.widget-five .widget-content .header-body h6 {
    font-weight: 700;
    font-size: 15px;
    letter-spacing: 0;
    margin-bottom: 0;
}
.widget-five .widget-content .header-body p {
    margin-bottom: 0;
    font-weight: 600;
    color: #888ea8;
    padding: 0;
}
.widget-five .widget-content .task-action {
    display: flex;
}
.widget-five .widget-content .task-action .dropdown.custom-dropdown {
    align-self: center;
}
.widget-five .widget-content .task-action .dropdown.custom-dropdown a.dropdown-toggle svg {
    color: #888ea8;
}
.widget-five .widget-content .task-action .dropdown.custom-dropdown .dropdown-menu {
    padding: 8px 8px;
    min-width: 10rem;
    border-radius: 6px;
    top: 5px !important;
}
.widget-five .w-content {
    text-align: center;
    height: 100%;
    padding: 20px 26px;
}
.widget-five .w-content div .task-left {
    margin-bottom: 0;
    font-size: 30px;
    color: #1b55e2;
    background: #c2d5ff;
    font-weight: 600;
    border-radius: 50%;
    display: inline-flex;
    height: 76px;
    width: 76px;
    justify-content: center;
    padding: 13px 0px;
    border: 5px solid #fff;
    margin-bottom: 20px;
    -webkit-box-shadow: 0px 0px 8px 2px #e0e6ed;
    box-shadow: 0px 0px 8px 2px #e0e6ed;
}
.widget-five .w-content div .task-completed {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 4px;
    color: #009688;
}
.widget-five .w-content div .task-hight-priority {
    color: #acb0c3;
    font-weight: 500;
    margin-bottom: 0;
}
.widget-five .w-content div .task-hight-priority span {
    color: #e7515a;
    font-weight: 700;
}

/*
    =====================
        Media Query
    =====================
*/

@media (max-width: 1199px) {
    .widget-activity-two .widget-content {
        padding-top: 0;
    }
    .widget-activity-two .mt-container {
        position: relative;
        height: 177px;
    }
}
@media (max-width: 575px) {
    .widget-card-four .w-content-img img {
        height: 94px;
    }
    .widget-notification-one .noti-action a span {
        display: none;
    }
    .widget-statistic .col-12:not(:last-child) .widget-one_hybrid {
        margin-bottom: 40px
    }
}