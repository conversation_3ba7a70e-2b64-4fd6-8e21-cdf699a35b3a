<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 6;
$sub_page = 60;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');

$id = 1;

if (isset($_REQUEST['submit'])) {

    $value[0]  = mysqli_real_escape_string($con, $_REQUEST['a']);
    $value[1]  = mysqli_real_escape_string($con, $_REQUEST['b']);
    $value[2]  = mysqli_real_escape_string($con, $_REQUEST['c']);

    for ($i = 0; $i < 3; $i++) {

        if ($value[$i] != '') {
            $status[$i] = dbQuery("UPDATE tabl_manualresultswitch SET switch='yes',value='" . $value[$i] . "' WHERE  id='" . ($i + 1) . "'");

            //echo "UPDATE tabl_manualresultswitch SET switch='yes',value='" . $value[$i] . "' WHERE  id='" . ($i + 1) . "'";
        } else {
            $status[$i] = dbQuery("UPDATE tabl_manualresultswitch SET switch='no',value=NULL WHERE  id='" . ($i + 1) . "'");

            //echo "UPDATE tabl_manualresultswitch SET switch='no',value=NULL WHERE  id='" . ($i + 1) . "'";
        }
    }

    if ($status[0] && $status[1] && $status[2]) {
        echo '<script>alert("Manual Result Updated!");window.location.href="manual_result.php"</script>';
    } else {
        echo '<script>alert("Something Went Wrong!");window.location.href="manual_result.php"</script>';
    }
}

$sel = dbQuery("SELECT * FROM tabl_manualresultswitch");
$i = 0;
while ($res = dbFetchAssoc($sel)) {
    $value[$i] = $res['value'];
    $i++;
}

$sel2 = dbQuery("SELECT * FROM tabl_gameid ORDER BY id DESC LIMIT 1");
$res2 = dbFetchAssoc($sel2);

$game_id = $res2['gameid'];


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Manual Result </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="manual_result.php">Manual Result</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Manual Result For <?php echo $game_id; ?></h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">

                                                                    <div class="col-sm-12 col-md-4">
                                                                        <div class="form-group">
                                                                            <label for="fullName">A</label>
                                                                            <input type="text" class="form-control mb-4" id="a" maxlength="1" name="a" value="<?php echo $value[0]; ?>" placeholder="A">

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-4">
                                                                        <div class="form-group">
                                                                            <label for="fullName">B</label>
                                                                            <input type="text" class="form-control mb-4" id="b" maxlength="1" name="b" value="<?php echo $value[1]; ?>" placeholder="B">

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-sm-12 col-md-4">
                                                                        <div class="form-group">
                                                                            <label for="fullName">C</label>
                                                                            <input type="text" class="form-control mb-4" id="c" maxlength="1" name="c" value="<?php echo $value[2]; ?>" placeholder="C">

                                                                        </div>
                                                                    </div>


                                                                    <!--<div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Description</label>
                                                                            <textarea class="form-control mb-4" id="description" name="description" placeholder="Description" rows="5" required><?php echo $res['description']; ?></textarea>
                                                                        </div>
                                                                        
                                                                    </div>-->



                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Update</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
    CKEDITOR.replace('description');
</script>