<?php
define('HOSTNAME', 'localhost');

// define ('USERNAME', 'hext203_db1');
// define ('PASSWORD', 'hext203_db1');
// define ('DATABASE_NAME', 'hext203_db1');

define('USERNAME', 'root');
define('PASSWORD', '');
define('DATABASE_NAME', 'predixo_showbaazi');


$con = mysqli_connect(HOSTNAME, USERNAME, PASSWORD, DATABASE_NAME);
if (mysqli_connect_errno()) {
	echo "Failed to connect to MySQL: " . mysqli_connect_error();
}


// date_default_timezone_set('Asia/Kolkata');


function dbQuery($sql)
{
	global $con;
	$result = mysqli_query($con, $sql);
	return $result;
}

function dbAffectedRows()
{
	global $con;
	return mysqli_affected_rows($con);
}

// function dbFetchArray($result, $resultType = MYSQL_NUM) {
// 	return mysqli_fetch_array($result, $resultType);
// }

function dbFetchAssoc($result)
{
	if ($result) {
		return mysqli_fetch_assoc($result);
	}
	return false;
}

function dbFetchRow($result)
{
	return mysqli_fetch_row($result);
}

function dbFreeResult($result)
{
	return mysqli_free_result($result);
}

function dbNumRows($result)
{
	return mysqli_num_rows($result);
}

// function dbSelect($con)
// {
// 	return mysqli_select_db($con);
// }

function dbInsertId()
{
	global $con;
	return mysqli_insert_id($con);
}

$setting = dbQuery("SELECT * FROM tabl_setting WHERE id=1");
$res_setting = dbFetchAssoc($setting);

define("SITE", $res_setting['site_name']);
define("EMAIL", $res_setting['site_email']);
define("MIN_RECHARGE", $res_setting['min_recharge']);
define("MIN_WITHDRAW", $res_setting['min_withdraw']);
define("USDT_RATE", $res_setting['usdt_to_inr_rate']);
define("INVITE_BONUS", $res_setting['invite_bonus']);
define("SELF_BONUS", $res_setting['self_bonus']);
define("LEVEL_1_COMM", $res_setting['level_1_comm']);
define("LEVEL_2_COMM", $res_setting['level_2_comm']);
define("LEVEL_3_COMM", $res_setting['level_3_comm']);

define("SALARY_SETTING", $res_setting['salary_setting']);

function update_wallet($user_id, $amount, $type)
{

	if ($type == 'credit') {
		$amount_new = check_wallet($user_id) + $amount;
	} else if ($type == 'debit') {
		$amount_new = check_wallet($user_id) - $amount;
	}
	$sel2 = dbQuery("UPDATE tabl_wallet SET amount='" . $amount_new . "' WHERE user_id='" . $user_id . "'");
}

function check_wallet($user_id)
{
	$sel = dbQuery("SELECT * FROM tabl_wallet WHERE user_id='" . $user_id . "'");
	$res = dbFetchAssoc($sel);

	// If wallet doesn't exist, create one
	if (!$res) {
		date_default_timezone_set('Asia/Kolkata');
		$date = date('Y-m-d H:i:s');

		$create_wallet = dbQuery("INSERT INTO tabl_wallet SET `user_id`='" . $user_id . "', `amount`='0', `safe_amount`='0', `envelopestatus`='0', `date`='" . $date . "'");

		if ($create_wallet) {
			// Return 0 for newly created wallet
			return 0;
		} else {
			// If wallet creation failed, return 0 as fallback
			return 0;
		}
	}

	return $res['amount'];
}


function update_wallet_balance($user_id, $amount)
{

	$amount_new = $amount;

	$sel2 = dbQuery("UPDATE tabl_wallet SET amount='" . $amount_new . "' WHERE user_id='" . $user_id . "'");

	return $sel2;
}



function update_safe_wallet($user_id, $amount, $type)
{
	if ($type == 'credit') {
		$amount_new = check_safe_wallet($user_id) + $amount;
	} else if ($type == 'debit') {
		$amount_new = check_safe_wallet($user_id) - $amount;
	}
	$sel2 = dbQuery("UPDATE tabl_wallet SET safe_amount='" . $amount_new . "' WHERE user_id='" . $user_id . "'");
}

function check_safe_wallet($user_id)
{
	$sel = dbQuery("SELECT * FROM tabl_wallet WHERE user_id='" . $user_id . "'");
	$res = dbFetchAssoc($sel);

	// If wallet doesn't exist, create one
	if (!$res) {
		date_default_timezone_set('Asia/Kolkata');
		$date = date('Y-m-d H:i:s');

		$create_wallet = dbQuery("INSERT INTO tabl_wallet SET `user_id`='" . $user_id . "', `amount`='0', `safe_amount`='0', `envelopestatus`='0', `date`='" . $date . "'");

		if ($create_wallet) {
			// Return 0 for newly created wallet
			return 0;
		} else {
			// If wallet creation failed, return 0 as fallback
			return 0;
		}
	}

	return $res['safe_amount'];
}


function update_safe_wallet_balance($user_id, $amount)
{

	$amount_new = $amount;

	$sel2 = dbQuery("UPDATE tabl_wallet SET safe_amount='" . $amount_new . "' WHERE user_id='" . $user_id . "'");

	return $sel2;
}




function transfer_amount($user_id, $amount, $transfer_to)
{
	date_default_timezone_set('Asia/Kolkata');
	$date = date('Y-m-d H:i:s');

	if ($transfer_to == 'safe') {
		if (check_wallet($user_id) >= $amount) {
			update_safe_wallet($user_id, $amount, "credit");
			update_wallet($user_id, $amount, "debit");
		} else {
			return 2;
		}

		$transfer_from = "main";
	} else if ($transfer_to == 'main') {
		if (check_safe_wallet($user_id) >= $amount) {
			update_wallet($user_id, $amount, "credit");
			update_safe_wallet($user_id, $amount, "debit");
		} else {
			return 3;
		}
		$transfer_from = "safe";
	}

	$result = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $user_id . "', order_id='0', amount='$amount', type='credit', actiontype='transfer_to_$transfer_to', date='" . $date . "'");
	$result2 = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $user_id . "', order_id='0', amount='$amount', type='debit', actiontype='transfer_from_$transfer_from', date='" . $date . "'");

	return $result && $result2 ? 1 : 0;
}





function invitebonus1($user_id, $refcode)
{
	date_default_timezone_set('Asia/Kolkata');

	$chksummery = dbQuery("SELECT * FROM `tabl_walletsummery` WHERE `user_id`='$user_id' and `actiontype`='recharge'");
	$chksummeryRow = dbNumRows($chksummery);
	if ($chksummeryRow == '1') {
		$userQuery = dbQuery("SELECT `id` from `tabl_user` WHERE `owncode`='$refcode'");
		$userResult = dbFetchAssoc($userQuery);
		$refuserid = $userResult['id'];
		$selectwallet = dbQuery("SELECT `amount` from `tabl_bonus` WHERE `user_id`='" . $refuserid . "'");
		$walletResult = dbFetchAssoc($selectwallet);
		$availableBalance = $walletResult['amount'];

		$sqlbonus = dbQuery("SELECT `bonusamount` from `tabl_paymentsetting` WHERE `id`='1'");
		$bonusResult = dbFetchAssoc($sqlbonus);
		$bonusAmount = $bonusResult['bonusamount'];
		$finalbonusbalance = $availableBalance + $bonusAmount;
		$today = date("Y-m-d H:i:s");

		$sqlbonuslevel1 = dbQuery("UPDATE `tabl_bonus` SET `amount` = '" . $finalbonusbalance . "',`level1` = '" . $finalbonusbalance . "' WHERE `user_id`= '" . $refuserid . "'");
		$sql = dbQuery("INSERT INTO `tabl_bonussummery`(`user_id`,`periodid`,`level1id`,`level2id`,`level1amount`,`level2amount`,`tradeamount`,`date`) VALUES ('" . $user_id . "','0','" . $refuserid . "','0','110','0','0','" . $today . "')");
	}
}


function userpromocode1($a, $user_id, $code, $tradeamount, $periodid)
{
	date_default_timezone_set('Asia/Kolkata');
	$today = date("Y-m-d H:i:s");

	$commissionQuery = mysqli_query($a, "SELECT * FROM `tabl_paymentsetting` WHERE `id`='1'");
	$commissionResult = dbFetchAssoc($commissionQuery);
	$level1commission = $commissionResult['level1'];
	$level2commission = $commissionResult['level2'];
	$level1 = ($tradeamount * $level1commission / 100);
	$level2 = ($tradeamount * $level2commission / 100);

	$userlevel1Query = mysqli_query($a, "SELECT `code`,(select `id` from `tabl_user` WHERE `owncode`='$code')level1id,(select `code` from `tabl_user` WHERE `owncode`='$code')level1code from `tabl_user` WHERE `id`='" . $user_id . "'");
	$userlevel1Result = dbFetchAssoc($userlevel1Query);
	$level1id = $userlevel1Result['level1id'];
	$level1code = $userlevel1Result['level1code'];
	//===============================================================================================
	$userlevel2Query = mysqli_query($a, "SELECT `id` from `tabl_user` WHERE `owncode`='" . $level1code . "'");
	$userlevel2Result = dbFetchAssoc($userlevel2Query);
	$level2id = $userlevel2Result['id'];
	//=================================================================================================
	$sql = mysqli_query($a, "INSERT INTO `tabl_bonussummery`(`user_id`,`periodid`,`level1id`,`level2id`,`level1amount`,`level2amount`,`tradeamount`,`date`) VALUES ('" . $user_id . "','" . $periodid . "','" . $level1id . "','" . $level2id . "','" . $level1 . "','" . $level2 . "','" . $tradeamount . "','" . $today . "')");
	$level1balance = bonus($a, 'level1', $level1id);
	$finallevel1balance = $level1balance + $level1;
	$bonusbalance1 = bonus($a, 'amount', $level1id);
	$finalbonusbalance1 = $bonusbalance1 + $level1;


	$level2balance = bonus($a, 'level2', $level2id);
	$finallevel2balance = $level2balance + $level2;

	$bonusbalance2 = bonus($a, 'amount', $level2id);
	$finalbonusbalance2 = $bonusbalance2 + $level2;


	$sqlbonuslevel1 = mysqli_query($a, "UPDATE `tabl_bonus` SET `amount` = '" . $finalbonusbalance1 . "',`level1` = '" . $finallevel1balance . "' WHERE `user_id`= '" . $level1id . "'");

	$sqlbonuslevel2 = mysqli_query($a, "UPDATE `tabl_bonus` SET `amount` = '" . $finalbonusbalance2 . "',`level2` = '" . $finallevel2balance . "' WHERE `user_id`= '" . $level2id . "'");
}


function getBaseUrl1()
{
	// output: /myproject/index.php
	$currentPath = $_SERVER['PHP_SELF'];

	// output: Array ( [dirname] => /myproject [basename] => index.php [extension] => php [filename] => index ) 
	$pathInfo = pathinfo($currentPath);

	// output: localhost
	$hostName = $_SERVER['HTTP_HOST'];

	// output: http://
	$protocol = strtolower(substr($_SERVER["SERVER_PROTOCOL"], 0, 5)) == 'https://' ? 'https://' : 'http://';

	// return: http://localhost/myproject/
	return $protocol . $hostName . $pathInfo['dirname'] . '/';
}







function check_invites($user_id, $step_no)
{
	$sel_ref_code = dbQuery("SELECT * FROM `tabl_user` WHERE `id`='$user_id'");
	if ($res_ref_code = dbFetchAssoc($sel_ref_code)) {
		$own_code = $res_ref_code['own_code'];
		$sel_user = dbQuery("SELECT COUNT('id') as total_invites from `tabl_user` WHERE `ref_code`='$own_code'");
		if ($res_user = dbFetchAssoc($sel_user)) {
			$sel_task = dbQuery("SELECT * FROM tabl_invitation_task WHERE `step_no`='$step_no'");
			if ($res_task = dbFetchAssoc($sel_task)) {
				if ($res_user['total_invites'] >= $res_task['no_of_invites']) {
					return $res_task['no_of_invites'];
				}
			}
			return $res_user['total_invites'];
		}
	}
	return 0;
}


function check_recharge($user_id, $step_no)
{
	// Fetch the user's own code
	$sel_user = dbQuery("SELECT own_code FROM `tabl_user` WHERE `id`='$user_id'");
	if ($res_user = dbFetchAssoc($sel_user)) {
		$own_code = $res_user['own_code'];

		// Fetch the total number of downlines who have recharged with at least 500
		$sel_downlines = dbQuery("SELECT COUNT(id) as total_downlines FROM `tabl_user` WHERE `ref_code`='$own_code' AND `id` IN (SELECT `user_id` FROM `tabl_deposits` WHERE `amount` >= 500 AND `status`='1')");
		if ($res_downlines = dbFetchAssoc($sel_downlines)) {

			// Fetch the task details
			$sel_task = dbQuery("SELECT * FROM tabl_invitation_task WHERE `step_no`='$step_no'");
			if ($res_task = dbFetchAssoc($sel_task)) {
				// Check if the total downlines meets the task requirement
				if ($res_downlines['total_downlines'] >= $res_task['no_of_invites']) {
					return $res_task['no_of_invites'];
				}
			}
			// Return the total downlines count if the task requirement is not met
			return $res_downlines['total_downlines'];
		}
	}
	// Return 0 if user or downlines not found
	return 0;
}


function check_invite_bonus_eligibility($user_id, $step_no)
{
	// Fetch the task details
	$sel_task = dbQuery("SELECT * FROM tabl_invitation_task WHERE `step_no`='$step_no'");
	if ($res_task = dbFetchAssoc($sel_task)) {

		if (check_invites($user_id, $step_no) >= $res_task['no_of_invites'] && check_recharge($user_id, $step_no) >= $res_task['no_of_invites']) {

			$sel_task_claim = dbQuery("SELECT * FROM tabl_invitation_task_claim WHERE user_id='$user_id' AND `step_no`='$step_no'");
			if ($res_task_claim = dbFetchAssoc($sel_task_claim)) {
				return 2;
			}

			return 1;
		}
	}

	// Return the total downlines count if the task requirement is not met
	return 0;
}




function check_bet_amount($user_id, $step_no)
{
	date_default_timezone_set('Asia/Kolkata');
	$date = date("Y-m-d");

	// Fetch the total betting amount of the current user for the specified step
	$betting_amount = 0;

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_anb_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_aviator_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_wheel_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_wingonew_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_wingo_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}


	$sel_task = dbQuery("SELECT * FROM tabl_activity_task WHERE `step_no`='$step_no'");
	if ($res_task = dbFetchAssoc($sel_task)) {

		if ($betting_amount >= $res_task['bet_amount']) {
			$betting_amount = $res_task['bet_amount'];
		}
	}


	return $betting_amount;
}



function check_activity_eligibility($user_id, $step_no)
{
	date_default_timezone_set('Asia/Kolkata');
	// $date = date("Y-m-d H:i:s");
	$date = date("Y-m-d");

	// echo $date;

	// Fetch the task details
	$sel_task = dbQuery("SELECT * FROM tabl_activity_task WHERE `step_no`='$step_no'");
	if ($res_task = dbFetchAssoc($sel_task)) {
		if (check_bet_amount($user_id, $step_no) >= $res_task['bet_amount']) {

			$sel_task_claim = dbQuery("SELECT * FROM tabl_activity_task_claim WHERE user_id='$user_id' AND `step_no`='$step_no' AND DATE(`date`)='$date'");
			if ($res_task_claim = dbFetchAssoc($sel_task_claim)) {
				return 2;
			}

			return 1;
		}
	}

	// Return the total downlines count if the task requirement is not met
	return 0;
}






function check_exp($user_id)
{
	$sel = dbQuery("SELECT SUM(exp) FROM tabl_experience WHERE user_id='" . $user_id . "'");
	$res = dbFetchAssoc($sel);

	return $res['SUM(exp)'];
}


function check_level($user_id)
{
	$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' ORDER BY id DESC");
	if ($res_user_level = dbFetchAssoc($sel_user_level)) {
		return $res_user_level['vip_level'];
	}

	return false;
}



function get_level_info($user_id)
{
	$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' ORDER BY id DESC");
	if ($res_user_level = dbFetchAssoc($sel_user_level)) {

		$level = $res_user_level['vip_level'];
		$sel_level = dbQuery("SELECT * FROM tabl_level WHERE `level`='$level'");
		if ($res_level = dbFetchAssoc($sel_level)) {
			return $res_level;
		} else {
			return 0;
		}
	}

	return 0;
}




function upgrade_level($user_id, $level)
{
	date_default_timezone_set('Asia/Kolkata');
	$date = date('Y-m-d H:i:s');
	$result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $level . "', date='" . $date . "'");
	if ($result) {
		$order_id = dbInsertId();

		if (dbQuery("UPDATE tabl_user SET vip_level='" . $level . "' WHERE `id` = '" . $user_id . "'")) {

			$sel_level = dbQuery("SELECT * FROM tabl_level WHERE `level`='$level'");
			if ($res_level = dbFetchAssoc($sel_level)) {
				$sel1 = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $user_id . "', order_id='$order_id', amount='" . $res_level['level_up_reward'] . "', type='credit', actiontype='level_up_reward', date='" . $date . "'");

				if ($sel1) {
					update_wallet($user_id, $res_level['level_up_reward'], 'credit');
				}

				return true;
			}
		}
	}

	return false;
}


function upgrade_vip_level1($user_id)
{
	date_default_timezone_set('Asia/Kolkata');
	$date = date('Y-m-d H:i:s');
	$experience = check_exp($user_id);

	$sel_level = dbQuery("SELECT * FROM tabl_level WHERE exp_required<='$experience' ORDER BY `level` DESC");
	if ($res_level = dbFetchAssoc($sel_level)) {
		// $result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $res_level['level'] . "', date='" . $date . "'");

		$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' AND vip_level='" . $res_level['level'] . "' ORDER BY id DESC");
		if (!$res_user_level = dbFetchAssoc($sel_user_level)) {
			return upgrade_level($user_id, $res_level['level']);
		}
	}

	return false;
}

function upgrade_vip_level($user_id)
{
	date_default_timezone_set('Asia/Kolkata');
	$date = date('Y-m-d H:i:s');
	$experience = check_exp($user_id);

	$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $user_id . "'");
	if ($user = dbFetchAssoc($userq)) {

		$sel_level = dbQuery("SELECT * FROM tabl_level WHERE exp_required<='$experience' AND `level` > '" . $user['vip_level'] . "'  ORDER BY `level`");
		if ($res_level = dbFetchAssoc($sel_level)) {
			// $result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $res_level['level'] . "', date='" . $date . "'");

			$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' AND vip_level='" . $res_level['level'] . "' ORDER BY id DESC");
			if (!$res_user_level = dbFetchAssoc($sel_user_level)) {
				return upgrade_level($user_id, $res_level['level']);
			}
		}
	}

	return false;
}





function betting_rebate($user_id, $total_amount, $game_id, $game, $bet_id)
{
	date_default_timezone_set('Asia/Kolkata');
	$date = date('Y-m-d H:i:s');

	$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $user_id . "'");
	if ($user = dbFetchAssoc($userq)) {
		$sel_level = dbQuery("SELECT * FROM tabl_level WHERE `level` = '" . $user['vip_level'] . "'");
		if ($res_level = dbFetchAssoc($sel_level)) {
			$rebate_rate = $res_level['rebate_rate'];
			$rebate = $total_amount * $rebate_rate / 100;

			$result = dbQuery("INSERT INTO `tabl_betting_rebate` SET `user_id`='$user_id', `bet_id`='$bet_id', `game`='$game', `game_id`='$game_id', `bet_amount`='$total_amount', `rebate_rate`='$rebate_rate', `rebate_amount`='$rebate', `date`='$date'");
			@$order_id = dbInsertId();

			if ($rebate) {
				$sel = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $user_id . "', order_id='$order_id', amount='$rebate', type='credit', actiontype='betting_rebate', date='" . $date . "'");

				if ($sel) {
					update_wallet($user_id, $rebate, 'credit');
				}
			}
		}
	}
}







function get_wingo_gameid($game_type)
{
	$game_id = '';

	$sel_game = dbQuery("SELECT game_id FROM `tabl_wingonew_gameid` WHERE game_type=$game_type ORDER BY id DESC LIMIT 1");
	// Fetch the result
	if ($res_game = dbFetchAssoc($sel_game)) {
		$game_id = $res_game['game_id'];
	}

	return $game_id;
}


function get_k3_gameid($game_type)
{
	$game_id = '';

	$sel_game = dbQuery("SELECT game_id FROM `tabl_k3_gameid` WHERE game_type=$game_type ORDER BY id DESC LIMIT 1");
	// Fetch the result
	if ($res_game = dbFetchAssoc($sel_game)) {
		$game_id = $res_game['game_id'];
	}

	return $game_id;
}









function wingo_get_bet_amt1($game_id, $game_type, $values, $type)
{

	if ($type == "tradeamount") {
		$result = dbQuery("SELECT SUM(amount)as tradeamount FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `acceptrule`='1'");

		// Loop through each row of the result set
		if ($row = dbFetchAssoc($result)) {
			return $row['tradeamount'];
		}
	} else if ($type == "winamount") {

		$winamount = 0;

		foreach ($values as $value) {
			if ($value == 'green' || $value == 'orange') {
				$multiplier = 2;
				$val = $value;
			} else if ($value == 'green2') {
				$multiplier = 1.5;
				$val = 'green';
			} else if ($value == 'orange2') {
				$multiplier = 1.5;
				$val = 'orange';
			} else if ($value == 'white') {
				$multiplier = 4.5;
				$val = $value;
			} else if ($value == 'big' || $value == 'small') {
				$multiplier = 2;
				$val = $value;
			} else {
				$multiplier = 9;
				$val = $value;
			}
			// Execute the SQL query and fetch the results, assuming $con is the database connection
			$result = dbQuery("SELECT * FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `value`='$val' AND `acceptrule`='1'");

			// Loop through each row of the result set
			while ($row = dbFetchAssoc($result)) {
				$winamount += ($row['amount'] - ($row['amount'] / 100 * 3)) * $multiplier;
			}
		}

		// Return the requested column value
		return $winamount;
	}
}



function wingo_get_bet_amt($game_id, $game_type, $values, $type)
{

	if ($type == "tradeamount") {
		$result = dbQuery("SELECT SUM(amount)as tradeamount FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `acceptrule`='1'");

		// Loop through each row of the result set
		if ($row = dbFetchAssoc($result)) {
			return $row['tradeamount'];
		}
	} else if ($type == "betamount") {

		$betamount = 0;

		foreach ($values as $value) {
			if ($value == 'green' || $value == 'orange') {
				$val = $value;
			} else if ($value == 'green2') {
				$val = 'green';
			} else if ($value == 'orange2') {
				$val = 'orange';
			} else if ($value == 'white') {
				$val = $value;
			} else if ($value == 'big' || $value == 'small') {
				$val = $value;
			} else {
				$val = $value;
			}

			// echo $game_id;
			// echo $game_type;
			// echo $val;

			// Execute the SQL query and fetch the results, assuming $con is the database connection
			$result = dbQuery("SELECT * FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `value`='$val' AND `acceptrule`='1'");

			// Loop through each row of the result set
			while ($row = dbFetchAssoc($result)) {
				$betamount += $row['amount'];
			}
		}

		// Return the requested column value
		return $betamount;
	}
}



function wingo_bet_amount($game_id, $game_type, $x)
{
	if ($x == 1 || $x == 3 || $x == 7 || $x == 9) { //======for green
		$color = 'green';
		$greentotal = wingo_get_bet_amt($game_id, $game_type, ['green'], 'betamount');


		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $greentotal + $big_small + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');
	} else if ($x == 2 || $x == 4 || $x == 6 || $x == 8) {
		$color = 'orange';
		$orangetotal = wingo_get_bet_amt($game_id, $game_type, ['orange'], 'betamount');

		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $orangetotal + $big_small + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');
	} else if ($x == 0) {
		$color = 'orange+white';
		// $orangetotal = wingo_winner($game_id, $game_type, ['orange', 'white2'], 'betamount');
		$orangetotal = wingo_get_bet_amt($game_id, $game_type, ['orange2'], 'betamount');
		$vtotal = wingo_get_bet_amt($game_id, $game_type, ['white'], 'betamount');

		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $orangetotal + $big_small + $vtotal + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');
	} else if ($x == 5) {
		$color = 'green+white';
		// $orangetotal = wingo_winner($game_id, $game_type, ['green', 'white2'], 'betamount');
		$orangetotal = wingo_get_bet_amt($game_id, $game_type, ['green2'], 'betamount');
		$vtotal = wingo_get_bet_amt($game_id, $game_type, ['white'], 'betamount');

		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $orangetotal + $big_small + $vtotal + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');
	}

	return $total;
}




function getTotalFirstRechargeAmount($ownCode)
{
	// Initialize total first recharge amount
	$totalFirstRechargeAmount = 0;

	// Query to select the minimum deposit date and amount for each user's first deposit
	$firstDepositQuery = dbQuery("SELECT `user_id`, MIN(`date`) AS first_deposit_date, SUM(`amount`) AS first_deposit_amount 
        FROM `tabl_deposits` 
        WHERE `user_id` IN (SELECT `id` FROM `tabl_user` WHERE `ref_code` = '$ownCode') AND `status`='1'
        GROUP BY `user_id`
    ");

	// Iterate through each user's first deposit dates and amounts
	while ($firstDepositResult = dbFetchAssoc($firstDepositQuery)) {
		// Add the deposit amount to the total first recharge amount
		$totalFirstRechargeAmount += $firstDepositResult['first_deposit_amount'];
	}

	// Query to select direct downlines
	$directDownlinesQuery = dbQuery("SELECT own_code FROM tabl_user WHERE ref_code = '$ownCode'");

	// Recursively calculate total first recharge amount for each direct downline
	while ($directDownline = dbFetchAssoc($directDownlinesQuery)) {
		$totalFirstRechargeAmount += getTotalFirstRechargeAmount($directDownline['own_code']);
	}

	return $totalFirstRechargeAmount;
}



function getTotalWinnings($user_id)
{
	// Fetch the total win amount of the current user for the specified step
	$win_amount = 0;

	// Query to get the total win amount for the current user and step
	$win_query = "SELECT SUM(amount) AS total_win_amount FROM `tabl_walletsummery` WHERE `user_id` = '$user_id' AND `actiontype`='win'";

	$sel_win = dbQuery($win_query);
	if ($res_win = dbFetchAssoc($sel_win)) {
		// Extract the total win amount
		$win_amount += $res_win['total_win_amount'];
	}

	// return number_format($win_amount, 2);
	return $win_amount;
}

function getTotalWithdrawal($user_id)
{
	// Fetch the total win amount of the current user for the specified step
	$withdrawal_amount = 0;

	// Query to get the total win amount for the current user and step
	$withdrawal_query = "SELECT SUM(amount) AS total_withdrawal FROM `tabl_walletsummery` WHERE `user_id` = '$user_id' AND `actiontype` LIKE 'withdraw%'";

	$sel_withdrawal = dbQuery($withdrawal_query);
	if ($res_withdrawal = dbFetchAssoc($sel_withdrawal)) {
		// Extract the total win amount
		$withdrawal_amount += $res_withdrawal['total_withdrawal'];
	}

	// return number_format($withdrawal_amount, 2);
	return $withdrawal_amount;
}


function getWithdrawalLimit($user_id)
{
	$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
	if (!$user = dbFetchAssoc($userq)) {
		echo "<script>window.location.href='./login.php';</script>";
	}

	$totalFirstRecharge = getTotalFirstRechargeAmount($user['own_code']);
	$FirstRecharge_limit = $totalFirstRecharge * 0.9;

	$totalWinnings = getTotalWinnings($user_id) >= 1000 ? getTotalWinnings($user_id) : 0;

	$total_widrawal_limit = $FirstRecharge_limit + $totalWinnings;

	$final_limit = $total_widrawal_limit - getTotalWithdrawal($user_id);
	// return $final_limit > 0 ? number_format($final_limit, 2) : 0;

	return $final_limit > 0 ? $final_limit : 0;
}





// Function to get direct subordinates with a total bet of at least 500 rupees today
function getDirectSubordinatesWithBets($user_id, $date_today)
{
	$user_ids = [];  // Array to store the user IDs of indirect subordinates

	$sql = "SELECT COUNT(DISTINCT u.id) AS direct_count, u.id as user_id
        FROM tabl_user u
        JOIN tabl_wingonew_betting b ON u.id = b.user_id
        WHERE u.ref_code = '$user_id' 
          AND DATE(b.date) = '$date_today' 
        GROUP BY u.id
        HAVING SUM(b.amount) >= 0
    ";
	// echo $sql . "<br>";

	$query = dbQuery($sql);
	while ($result = dbFetchAssoc($query)) {
		$user_ids[] = $result['user_id'];  // Collect user_id of each qualifying direct subordinate
	}

	return $user_ids;
}



// Function to count indirect subordinates up to 6 levels with a minimum of 500 rupees in bets
function getIndirectSubordinatesWithBets($user_id, $date_today, $level = 1, $max_level = 6)
{
	$user_ids = [];  // Array to store the user IDs of indirect subordinates

	if ($level > $max_level) {
		return 0;  // Stop if max level is exceeded
	}
	$count = 0;

	// Query to find direct subordinates at the current level
	$sql = "SELECT u.id, u.own_code, u.id as user_id FROM tabl_user u
        JOIN tabl_wingonew_betting b ON u.id = b.user_id
        WHERE u.ref_code = '$user_id' 
          AND DATE(b.date) = '$date_today'
        GROUP BY u.id
        HAVING SUM(b.amount) >= 0
    ";
	// echo $sql . "<br>";

	$subordinates_query = dbQuery($sql);
	while ($subordinate = dbFetchAssoc($subordinates_query)) {
		$subordinate_id = $subordinate['own_code'];
		$user_ids[] = $subordinate['id'];  // Collect user_id of each qualifying subordinate at this level
		// Recursively collect user_ids of subordinates of the current subordinate
		$user_ids = array_merge($user_ids, getIndirectSubordinatesWithBets($subordinate_id,  $date_today, $level + 1, $max_level));
	}

	return $user_ids;
}



function getTotalDepositAndCount($user_ids, $date_today)
{

	// Convert user_ids array to a comma-separated string for SQL query
	$user_ids_str = implode(",", array_map('intval', $user_ids));

	// Initialize variables for total deposit and count of users with deposit > 500
	$total_deposit = 0;
	$count_above_500 = 0;

	// Query to get total deposit and count users with deposit > 500
	$sql = "SELECT user_id, SUM(amount) as total_amount
            FROM tabl_deposits
            WHERE user_id IN ($user_ids_str) 
              AND DATE(date) = '$date_today'
            GROUP BY user_id";

	$query = dbQuery($sql);

	while ($result = dbFetchAssoc($query)) {
		$total_deposit += $result['total_amount'];  // Accumulate total deposit

		// Check if this user has deposited more than 500
		if ($result['total_amount'] >= 500) {
			$count_above_500 += 1;
		}
	}

	return [
		'total_deposit' => $total_deposit,
		'count_above_500' => $count_above_500
	];
}




function distributeCommission($user_id, $trade_amount, $game_id, $game_type, $bet_id)
{

	// Get the current date and time
	date_default_timezone_set('Asia/Kolkata');
	$today = date("Y-m-d H:i:s");

	// Calculate commissions for level 1, 2, and 3
	$level1_commission = ($trade_amount * LEVEL_1_COMM / 100);
	$level2_commission = ($trade_amount * LEVEL_2_COMM / 100);
	$level3_commission = ($trade_amount * LEVEL_3_COMM / 100);

	// Retrieve level 1 upline information
	$userLevel1Query = dbQuery("SELECT `ref_code` FROM `tabl_user` WHERE `id`='" . $user_id . "'");
	if ($userLevel1Result = dbFetchAssoc($userLevel1Query)) {
		$level1Code = $userLevel1Result['ref_code'];

		// if ($level1Code) {
		// Retrieve level 1 user ID
		$level1UserQuery = dbQuery("SELECT `id`, `ref_code` FROM `tabl_user` WHERE `own_code`='" . $level1Code . "'");
		if ($level1UserResult = dbFetchAssoc($level1UserQuery)) {
			$level1_id = $level1UserResult['id'];
			$level2Code = $level1UserResult['ref_code'];

			// if ($level2Code) {
			// Retrieve level 2 user ID
			$level2UserQuery = dbQuery("SELECT `id`, `ref_code` FROM `tabl_user` WHERE `own_code`='" . $level2Code . "'");
			if ($level2UserResult = dbFetchAssoc($level2UserQuery)) {
				$level2_id = $level2UserResult['id'];
				$level3Code = $level2UserResult['ref_code'];

				// if ($level3Code) {
				// Retrieve level 3 user ID
				$level3UserQuery = dbQuery("SELECT `id` FROM `tabl_user` WHERE `own_code`='" . $level3Code . "'");
				if ($level3UserResult = dbFetchAssoc($level3UserQuery)) {
					$level3_id = $level3UserResult['id'];
				}
			}
		}

		// Insert records into `tabl_bonussummery`
		if ($level1_id) {
			dbQuery("INSERT INTO `tabl_bonussummery` (`user_id`, `bet_id`, `game_type`, `game_id`, `level`, `receiver_id`, `bet_amount`, `bonus_amount`, `date`) 
                     VALUES ('" . $user_id . "', '" . $bet_id . "', '" . $game_type . "', '" . $game_id . "', 1, '" . $level1_id . "', '" . $trade_amount . "', '" . $level1_commission . "', '" . $today . "')");
			$bonus_id = dbInsertId();

			update_wallet($level1_id, $level1_commission, 'credit');
			updateWalletSummery($level1_id, $level1_commission, $game_type, $game_id, 'credit', 'level_1_income');
		}

		if ($level2_id) {
			dbQuery("INSERT INTO `tabl_bonussummery` (`user_id`, `bet_id`, `game_type`, `game_id`, `level`, `receiver_id`, `bet_amount`, `bonus_amount`, `date`) 
                     VALUES ('" . $user_id . "', '" . $bet_id . "', '" . $game_type . "', '" . $game_id . "', 2, '" . $level2_id . "', '" . $trade_amount . "', '" . $level2_commission . "', '" . $today . "')");
			$bonus_id = dbInsertId();

			update_wallet($level2_id, $level2_commission, 'credit');
			updateWalletSummery($level2_id, $level2_commission, $game_type, $game_id, 'credit', 'level_2_income');
		}

		if ($level3_id) {
			dbQuery("INSERT INTO `tabl_bonussummery` (`user_id`, `bet_id`, `game_type`, `game_id`, `level`, `receiver_id`, `bet_amount`, `bonus_amount`, `date`) 
                     VALUES ('" . $user_id . "', '" . $bet_id . "', '" . $game_type . "', '" . $game_id . "', 3, '" . $level3_id . "', '" . $trade_amount . "', '" . $level3_commission . "', '" . $today . "')");
			$bonus_id = dbInsertId();

			update_wallet($level3_id, $level3_commission, 'credit');
			updateWalletSummery($level3_id, $level3_commission, $game_type, $game_id, 'credit', 'level_3_income');
		}
	}
}


// function updateWalletSummery($user_id, $amount, $game_type, $game_id, $type, $actiontype)
function updateWalletSummery($user_id, $amount, $game_type, $game_id, $type, $actiontype)
{
	date_default_timezone_set('Asia/Kolkata');
	$today = date("Y-m-d H:i:s");

	dbQuery("INSERT INTO `tabl_walletsummery` (`user_id`, `order_id`, `amount`, `game`, `type`, `actiontype`, `date`) 
             VALUES ('" . $user_id . "', '" . $game_id . "', '" . $amount . "', '" . $game_type . "', '" . $type . "', '" . $actiontype . "', '" . $today . "')");

	return dbInsertId();
}



function getCommissionByDate($user_id, $date_type)
{
	$date_condition = "";

	// Determine the date range based on the input
	if ($date_type == 'today') {
		$date_condition = "DATE(`date`) = CURDATE()";
	} elseif ($date_type == 'yesterday') {
		$date_condition = "DATE(`date`) = CURDATE() - INTERVAL 1 DAY";
	} else {
		// Assuming $date_type is a specific date in 'Y-m-d' format
		$date_condition = "DATE(`date`) = '$date_type'";
	}

	// Query to get the total commission for the specified date
	$query = "
        SELECT SUM(bonus_amount) AS total_commission 
        FROM `tabl_bonussummery` 
        WHERE `receiver_id` = '$user_id' 
        AND $date_condition
    ";

	$result = dbQuery($query);
	$commission_data = dbFetchAssoc($result);

	// Return the total commission (or 0 if no commission found)
	return $commission_data['total_commission'] ?? 0;
}






$game_max_loss = 0;
$game_setting = 0;

$sel_gamesetting = dbQuery("SELECT * FROM tabl_gamesettings WHERE `game`='aviator'");
if ($res_gamesetting = dbFetchAssoc($sel_gamesetting)) {
	$game_max_loss = $res_gamesetting['max_loss'];
	$game_setting = $res_gamesetting['settingtype'];
}


// $base_url = "http://127.0.0.2/showbaazi/";
// $base_url = "https://showbaazi.com/";

$base_url = "https://showbaazi.com/games/";
