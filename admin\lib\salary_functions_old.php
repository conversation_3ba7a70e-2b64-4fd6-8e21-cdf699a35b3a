<?php

function getDirectSubordinates($own_code, $date_today)
{
    $sql = "
        SELECT u.id, u.own_code, SUM(b.amount) as total_bet 
        FROM tabl_user u
        LEFT JOIN tabl_wingonew_betting b ON u.id = b.user_id AND DATE(b.date) = '$date_today'
        WHERE u.ref_code = '$own_code'
        GROUP BY u.id
    ";
    $result = dbQuery($sql);
    $subordinates = [];
    while ($row = dbFetchAssoc($result)) {
        $subordinates[] = [
            'id' => $row['id'],
            'own_code' => $row['own_code'],
            'total_bet' => $row['total_bet'] ?? 0, // Default to 0 if null
            'meets_threshold' => ($row['total_bet'] ?? 0) >= 500,
        ];
    }
    return $subordinates;
}



function getIndirectSubordinates($own_code, $date_today, $level = 1, $max_level = 6)
{
    if ($level > $max_level) {
        return [];
    }

    $direct_subordinates = getDirectSubordinates($own_code, $date_today);
    $all_subordinates = $direct_subordinates;

    foreach ($direct_subordinates as $subordinate) {
        $indirect_subordinates = getIndirectSubordinates($subordinate['own_code'], $date_today, $level + 1, $max_level);
        $all_subordinates = array_merge($all_subordinates, $indirect_subordinates);
    }

    return $all_subordinates;
}


function getAllSubordinates($own_code, $date_today, $max_level = 6)
{
    $sql = "
        WITH RECURSIVE subordinate_hierarchy AS (
            -- Non-recursive part: Get direct subordinates
            SELECT 
                u.id AS user_id, 
                u.own_code AS own_code, 
                u.ref_code AS parent_code,
                1 AS level
            FROM tabl_user u
            WHERE u.ref_code = '$own_code'

            UNION ALL

            -- Recursive part: Get indirect subordinates
            SELECT 
                u.id AS user_id, 
                u.own_code AS own_code, 
                u.ref_code AS parent_code,
                sh.level + 1 AS level
            FROM tabl_user u
            INNER JOIN subordinate_hierarchy sh 
                ON u.ref_code = sh.own_code
            WHERE sh.level < $max_level
        )
        -- Aggregate betting totals after recursion
        SELECT 
            sh.user_id, 
            sh.own_code, 
            sh.level,
            COALESCE(SUM(b.amount), 0) AS total_bet
        FROM subordinate_hierarchy sh
        LEFT JOIN tabl_wingonew_betting b 
            ON sh.user_id = b.user_id AND DATE(b.date) = '$date_today'
        GROUP BY sh.user_id, sh.level
        ORDER BY sh.level, sh.user_id;
    ";

    $result = dbQuery($sql);
    $subordinates = [];
    while ($row = dbFetchAssoc($result)) {
        $subordinates[] = [
            'id' => $row['user_id'],
            'own_code' => $row['own_code'],
            'total_bet' => $row['total_bet'] ?? 0, // Default to 0 if null
            'level' => $row['level'],
            'meets_threshold' => ($row['total_bet'] ?? 0) >= 500,
        ];
    }
    return $subordinates;
}





function getTotalDepositAndCount($indirect_list, $date_today)
{

    // Extract user IDs from the indirect list
    $user_ids = array_column($indirect_list, 'id'); // Assumes 'user_id' key exists in the indirect list

    // If no user IDs are found, return default values
    if (empty($user_ids)) {
        return ['total_deposit' => 0, 'count_above_500' => 0];
    }


    // Convert user IDs to a comma-separated string for SQL query
    $user_ids_str = implode(",", array_map('intval', $user_ids));
    $total_deposit = 0;
    $count_above_500 = 0;

    // Query to fetch deposit data grouped by user
    $sql = "
        SELECT user_id, SUM(amount) AS total_amount 
        FROM tabl_deposits 
        WHERE user_id IN ($user_ids_str) AND DATE(date) = '$date_today'
        GROUP BY user_id
    ";

    $query = dbQuery($sql);
    while ($result = dbFetchAssoc($query)) {
        // Accumulate total deposit
        $total_deposit += $result['total_amount'];

        // Increment count if the deposit is above 500
        if ($result['total_amount'] >= 500) {
            $count_above_500++;
        }
    }

    return [
        'total_deposit' => $total_deposit,
        'count_above_500' => $count_above_500
    ];
}






function computeSalary($user_id, $trade_total, $direct_list, $indirect_list, $date_today)
{
    // Count direct and indirect members who meet the threshold
    $direct_count = count(array_filter($direct_list, fn($item) => $item['meets_threshold']));
    $indirect_count = count(array_filter($indirect_list, fn($item) => $item['meets_threshold']));

    // Calculate team members excluding direct subordinates
    $team_members = $indirect_count - $direct_count;

    $depositors_count = 0;

    // Calculate depositors and total deposit from subordinate lists
    $deposit_data = getTotalDepositAndCount($indirect_list, $date_today);
    $depositors_count = $deposit_data['count_above_500'];
    $total_deposit = $deposit_data['total_deposit'];

    // Fetch salary data based on eligibility
    $sql_salary = "SELECT * 
        FROM tabl_salary 
        WHERE direct_members <= '$direct_count' AND (team_members <= '$team_members' OR total_members <= '$indirect_count' ) AND status = '1'
        ORDER BY total_members DESC LIMIT 1
    ";
    // echo $sql_salary . "<br>";

    $salary_row = dbFetchAssoc(dbQuery($sql_salary));

    if ($salary_row) {
        $salary_id = $salary_row['id'];
        $salary = $salary_row['salary'];

        // Calculate depositors and total deposit from subordinate lists
        // $deposit_data = getTotalDepositAndCount($indirect_list, $date_today);
        // $depositors_count = $deposit_data['count_above_500'];
        // $total_deposit = $deposit_data['total_deposit'];

        // echo "Direct Count: " . $direct_count . "<br>";
        // echo "Team Members: " . $team_members . "<br>";

        // echo 'depositors_count:' . $depositors_count . "<br>";
        // echo 'total_deposit:' . $total_deposit . "<br>";

        // echo $salary * 0.1;

        // Maximum salary calculation based on deposits
        $max_salary = $total_deposit * 0.1;

        // Today's income logic
        $today_income = ($depositors_count * 2 >= $salary_row['total_members'])
            ? min($salary, $max_salary)
            : min(min(20 * $indirect_count, $salary), $max_salary);

        // : min(20 * $depositors_count, $max_salary);

        return [
            'valid' => true,
            'salary_id' => $salary_id,
            'amount' => $today_income,
            'direct_count' => $direct_count,
            'indirect_count' => $team_members,
            'depositors_count' => $depositors_count,
            'total_deposit' => $total_deposit,

        ];
    } else {
        // echo "No salary data found.<br>";
    }

    // Return invalid result if no salary row matches
    return [
        'valid' => false,
        'salary_id' => 0,
        'amount' => 0,
        'direct_count' => $direct_count,
        'indirect_count' => $team_members,
        'depositors_count' => $depositors_count,
        'total_deposit' => $total_deposit,
    ];
}




function computeSalaryPer($direct_list, $indirect_list, $date_today)
{
    // Count direct and indirect members who meet the threshold
    // $direct_count = count(array_filter($direct_list, fn($item) => $item['meets_threshold']));
    // $indirect_count = count(array_filter($indirect_list, fn($item) => $item['meets_threshold']));

    $direct_count = count($direct_list); // Count all items in the direct list
    $indirect_count = count($indirect_list); // Count all items in the indirect list

    // Calculate team members excluding direct subordinates
    $team_members = $indirect_count - $direct_count;


    // Fetch salary data based on eligibility
    // $sql_salary = "SELECT * 
    //     FROM tabl_salary 
    //     WHERE direct_members <= '$direct_count' AND (team_members <= '$team_members' OR total_members <= '$indirect_count' ) AND status = '1'
    //     ORDER BY total_members DESC LIMIT 1
    // ";

    $sql_salary = "SELECT * 
        FROM tabl_salary 
        WHERE direct_members <= '$direct_count' AND status = '1'
        ORDER BY total_members DESC LIMIT 1
    ";
    echo $sql_salary . "<br>";

    $salary_row = dbFetchAssoc(dbQuery($sql_salary));

    if ($salary_row) {
        $salary_id = $salary_row['id'];
        $salary_per = $salary_row['salary_per'];
        $min_deposit = $salary_row['min_deposit'];

        // Calculate depositors and total deposit from subordinate lists
        $deposit_data = getTotalDepositAndCount($indirect_list, $date_today);
        $depositors_count = $deposit_data['count_above_500'];
        $total_deposit = $deposit_data['total_deposit'];

        // echo "Direct Count: " . $direct_count . "<br>";
        // echo "Team Members: " . $team_members . "<br>";

        // echo 'depositors_count:' . $depositors_count . "<br>";
        // echo 'total_deposit:' . $total_deposit . "<br>";

        // echo $salary * 0.1;

        // Maximum salary calculation based on deposits
        $max_salary = $total_deposit * 0.1;


        if ($total_deposit >= $min_deposit) {

            $salary = $total_deposit * ($salary_per / 100); // Salary calculation based on deposits and salary percentage
            $today_income =  min($salary, $max_salary); // Ensure salary does not exceed maximum salary based on deposits

            return [
                'valid' => true,
                'salary_id' => $salary_id,
                'amount' => $today_income
            ];
        }
    } else {
        echo "No salary data found.<br>";
    }

    // Return invalid result if no salary row matches
    return ['valid' => false];
}
