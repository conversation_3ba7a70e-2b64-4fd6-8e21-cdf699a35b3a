<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 6;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {

    $cust = $_POST['user_id'];
    $users = '';

    if (isset($cust)) {
        $i = 0;
        foreach ($cust as $cus) {
            if ($i < 1) {
                $users = $cus;
            } else {
                $users = $users . ',' . $cus;
            }
            $i++;
        }
    } else {
        $users = '0';
    }

    $coupon_name = mysqli_real_escape_string($con, $_REQUEST['name']);
    $coupon_code = mysqli_real_escape_string($con, $_REQUEST['coupon_code']);

    $type = mysqli_real_escape_string($con, $_REQUEST['type']);
    $rate = mysqli_real_escape_string($con, $_REQUEST['rate']);
    $price = mysqli_real_escape_string($con, $_REQUEST['price']);
    $discount = mysqli_real_escape_string($con, $_REQUEST['discount']);

    $description = mysqli_real_escape_string($con, $_REQUEST['description']);

    if ($_FILES["image"]["name"] != "") {

        $target_dir = "../assets/img/coupon/";
        $name = rand(10000, 1000000);
        $extension = pathinfo($_FILES["image"]["name"], PATHINFO_EXTENSION);
        $new_name = $name . "." . $extension;
        $target_file = $target_dir . $name . "." . $extension;

        $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
        if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
            die("This is not valid image. Please try again.");
        } else {
            move_uploaded_file($_FILES["image"]["tmp_name"], $target_file);
            $target_path = $target_dir . $new_name;
            $resizeObj = new resize($target_dir . $new_name);
            $resizeObj->resizeImage(100, 60, 'exact');
            $resizeObj->saveImage($target_dir . "thumb-100/" . $new_name, 100);

            dbQuery("UPDATE tabl_coupon SET name='" . $coupon_name . "',coupon_code='" . $coupon_code . "',type='" . $type . "',rate='" . $rate . "',price='" . $price . "',discount='" . $discount . "',image='" . $new_name . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',allow_user='" . $users . "' WHERE id='" . $_REQUEST['id'] . "'");
        }
    } else {
        dbQuery("UPDATE tabl_coupon SET name='" . $coupon_name . "',coupon_code='" . $coupon_code . "',type='" . $type . "',rate='" . $rate . "',price='" . $price . "',discount='" . $discount . "',description='" . mysqli_real_escape_string($con, $_REQUEST['description']) . "',allow_user='" . $users . "' WHERE id='" . $_REQUEST['id'] . "'");
    }
    echo '<script>alert("Coupon Updated!");window.location.href="coupon.php"</script>';
}

$sel = dbQuery("SELECT * FROM tabl_coupon WHERE id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Edit Coupons </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN THEME GLOBAL STYLES -->
    <link href="plugins/flatpickr/flatpickr.css" rel="stylesheet" type="text/css">
    <link href="plugins/noUiSlider/nouislider.min.css" rel="stylesheet" type="text/css">
    <!-- END THEME GLOBAL STYLES -->

    <!--  BEGIN CUSTOM STYLE FILE  -->

    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/scrollspyNav.css" rel="stylesheet" type="text/css" />
    <link href="plugins/flatpickr/custom-flatpickr.css" rel="stylesheet" type="text/css">
    <link href="plugins/noUiSlider/custom-nouiSlider.css" rel="stylesheet" type="text/css">
    <link href="plugins/bootstrap-range-Slider/bootstrap-slider.css" rel="stylesheet" type="text/css">
    <!--  END CUSTOM STYLE FILE  -->

    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/semantic-ui@2.2.13/dist/semantic.min.css'>

    <style>
        .ui.fluid.dropdown {
            overflow-y: scroll;
            height: 100px;
        }

        .ui.fluid.dropdown:focus {
            overflow-y: visible;
            height: 100px;
        }

        .ui.fluid.dropdown:active {
            overflow-y: visible;
            height: 100px;
        }
    </style>


</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="coupon.php">Coupons</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Edit</a></li>
                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Edit Coupons</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="form-row">
                                                                    <div class="form-group col-md-12">
                                                                        <label for="fullName"><strong>Coupon Name</strong></label>
                                                                        <input type="text" class="form-control" id="name" name="name" placeholder="Coupon Name" value="<?php echo $res['name'] ?>" required>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Coupon Code</strong></label>
                                                                        <input type="text" class="form-control" id="coupon_code" name="coupon_code" placeholder="Coupon Code" value="<?php echo $res['coupon_code'] ?>" required>
                                                                    </div>

                                                                    <div class="form-group col-md-6">
                                                                        <label for="fullName"><strong>Type</strong></label>
                                                                        <select name="type" id="type" class="form-control" required>
                                                                            <option value="">Select</option>
                                                                            <?php if ($res['type'] == 'free') { ?>
                                                                                <option value="free" selected>Free</option>
                                                                                <option value="flat">Flat</option>
                                                                                <option value="per">Persent</option>
                                                                            <?php } else if ($res['type'] == 'flat') { ?>
                                                                                <option value="free">Free</option>
                                                                                <option value="flat" selected>Flat</option>
                                                                                <option value="per">Persent</option>
                                                                            <?Php } else if ($res['type'] == 'per') { ?>
                                                                                <option value="free">Free</option>
                                                                                <option value="flat">Flat</option>
                                                                                <option value="per" selected>Persent</option>
                                                                            <?Php } else { ?>
                                                                                <option value="free">Free</option>
                                                                                <option value="flat">Flat</option>
                                                                                <option value="per">Persent</option>
                                                                            <?php } ?>
                                                                        </select>

                                                                    </div>
                                                                </div>

                                                                <div class="form-row">
                                                                    <div class="form-group col-md-4">
                                                                        <label for="fullName"><strong>Rate</strong></label>
                                                                        <input type="number" class="form-control" id="rate" name="rate" placeholder="Rate" value="<?php echo $res['rate'] ?>" required>
                                                                    </div>
                                                                    <div class="form-group col-md-4">
                                                                        <label for="fullName"><strong>Price</strong></label>
                                                                        <input type="number" class="form-control" id="price" name="price" placeholder="Price" value="<?php echo $res['price'] ?>" required>
                                                                    </div>
                                                                    <div class="form-group col-md-4">
                                                                        <label for="fullName"><strong>Discount</strong></label>
                                                                        <input type="number" class="form-control" id="discount" name="discount" placeholder="Discount" value="<?php echo $res['discount'] ?>" required>
                                                                    </div>

                                                                </div>

                                                                <div class="form-row">
                                                                    <div class="form-group col-md-12">
                                                                        <label for="fullName"><strong>Allowed Users</strong></label>
                                                                        <select name="user_id[]" id="" multiple="" onChange="set_cat(this.value)" required class="form-control label ui selection fluid dropdown">
                                                                            <option value="">SELECT</option>
                                                                            <?php $user = dbQuery("SELECT * FROM tabl_user ORDER BY name ASC");
                                                                            while ($res_customer = dbFetchAssoc($user)) {

                                                                                $arr = explode(",", $res['location_id']);
                                                                                if (in_array($res_location['id'], $arr)) {
                                                                                    $selected = 'selected';
                                                                                } else {
                                                                                    $selected = '';
                                                                                }
                                                                            ?>
                                                                                <option value="<?php echo $res_customer['id']; ?>" <?php echo $selected; ?>><?php echo $res_customer['name']; ?></option>
                                                                            <?php } ?>

                                                                        </select>
                                                                    </div>
                                                                </div>


                                                                <div class="row">

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"><strong>Full Description</strong></label>
                                                                            <textarea class="form-control" id="description" name="description" placeholder="Description" required rows="8"><?php echo $res['description'] ?></textarea>
                                                                        </div>

                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Image</label>
                                                                            <img src="../assets/img/coupon/thumb-100/<?php echo $res['image']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Update Image</label>
                                                                            <input type="file" class="form-control mb-4" id="image" name="image" placeholder="Image">
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Submit</button>
                                                                        </div>

                                                                    </div>



                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="plugins/jquery-ui/jquery-ui.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>

    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js'></script>
    <script src='https://cdn.jsdelivr.net/npm/semantic-ui@2.2.13/dist/semantic.min.js'></script>
    <script src="./assets/select/script.js"></script>

    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="plugins/highlight/highlight.pack.js"></script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL SCRIPTS -->
    <script src="assets/js/scrollspyNav.js"></script>
    <script src="plugins/flatpickr/flatpickr.js"></script>
    <script src="plugins/noUiSlider/nouislider.min.js"></script>

    <script src="plugins/flatpickr/custom-flatpickr.js"></script>
    <script src="plugins/noUiSlider/custom-nouiSlider.js"></script>
    <script src="plugins/bootstrap-range-Slider/bootstrap-rangeSlider.js"></script>

    <script>
        function displayResult()

        {

            var i = document.getElementById("num").value;

            document.getElementById("classes").insertRow(-1).innerHTML = '<tr><td> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-plus-square" onClick="displayResult()" name="add" style="cursor:pointer;"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg></td><td>' + i + '</td><td><select name="class_type_' + i + '" id="class_type_' + i + '" class="form-control"><option value="0">SELECT</option><option value="1">Pre-Recorded</option><option value="2">Free Classes</option></select></td><td><select name="type_' + i + '" id="type_' + i + '" class="form-control" onChange="set_class_type(' + i + ',this.value)"><option value="0">SELECT</option><option value="1">PDF</option><option value="2">URL</option></select></td><td><input type="text" name="file_' + i + '" id="doc_type_' + i + '"  class="form-control" accept="application/pdf"></td></tr>';
            i++;

            var num = document.getElementById("num").value = i;

        }
    </script>

    <script>
        function isNumber(evt) {
            var iKeyCode = (evt.which) ? evt.which : evt.keyCode
            if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
                return false;

            return true;
        }

        function isDecimal(evt, obj) {

            var charCode = (evt.which) ? evt.which : event.keyCode
            var value = obj.value;
            var dotcontains = value.indexOf(".") != -1;
            if (dotcontains)
                if (charCode == 46) return false;
            if (charCode == 46) return true;
            if (charCode > 31 && (charCode < 48 || charCode > 57))
                return false;
            return true;
        }
    </script>
    <script>
        function set_sub_cat(cat_id) {
            $.ajax({
                url: 'ajax/get_sub_category.php',
                type: 'post',
                data: {
                    'cat_id': cat_id
                },
                success: function(data) {
                    if (data != "") {
                        $("#sub_cat_id").html(data);
                    } else {
                        $("#sub_cat_id").html('<option value="0">SELECT</option>');

                    }
                },
            });

        }
    </script>
    <script>
        function set_class_type(id, val) {
            if (val == 1) {
                $("#doc_type_" + id).attr('type', 'file');
            } else {
                $("#doc_type_" + id).attr('type', 'text');
            }
        }
    </script>
</body>

</html>