<div class="topbar-nav header navbar" role="banner">
    <nav id="topbar">
        <ul class="navbar-nav theme-brand flex-row  text-center">
            <li class="nav-item theme-text">
                <a href="home.php" class="nav-link">
                    <?php echo SITE; ?>
                </a>
            </li>
        </ul>

        <ul class="list-unstyled menu-categories" id="topAccordion">

            <li class="menu single-menu <?php if ($page == 1) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="home.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-home">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                        <span>Home</span>
                    </div>

                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 2) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="user.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Manage User</span>
                    </div>

                </a>
            </li>

            <!-- <li class="menu single-menu 
                <?php echo ($page == 3) ? 'active' : ''; ?>">
                <a href="main_category.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Main Category</span>
                    </div>
                </a>
            </li>

            <li class="menu single-menu <?php echo ($page == 5) ? 'active' : ''; ?>">
                <a href="products.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Products</span>
                    </div>
                </a>
            </li> -->


            <!-- <li class="menu single-menu 
                <?php if ($page == 4) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="property_type.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Property Type</span>
                    </div>
                </a>
            </li> -->


            <!-- <li class="menu single-menu 
                <?php if ($page == 5) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="plans.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Plans</span>
                    </div>
                </a>
            </li> -->


            <li class="menu single-menu <?php echo ($page == 3) ? 'active' : ''; ?>">
                <a href="#app3" aria-expanded="false" class="dropdown-toggle" data-toggle="collapse">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Recharge</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app3" data-parent="#topAccordion">

                    <li class="<?php echo ($sub_page == 30) ? 'active' : ''; ?>">
                        <a href="recharge.php">Recharge</a>
                    </li>
                    <li class="<?php echo ($sub_page == 31) ? 'active' : ''; ?>">
                        <a href="pending_recharge.php">Pending Recharge</a>
                    </li>

                    <!-- <li class="<?php echo ($sub_page == 32) ? 'active' : ''; ?>">
                        <a href="approved_recharge.php">Approved Recharge</a>
                    </li>
                    <li class="<?php echo ($sub_page == 33) ? 'active' : ''; ?>">
                        <a href="rejected_recharge.php">Rejected Recharge</a>
                    </li> -->

                    <li class="<?php echo ($sub_page == 34) ? 'active' : ''; ?>">
                        <a href="payment_setting.php">Payment Setting</a>
                    </li>
                </ul>
            </li>


            <li class="menu single-menu <?php echo ($page == 4) ? 'active' : ''; ?>">
                <a href="#app4" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Withdrawal</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app4" data-parent="#topAccordion">
                    <li class="<?php echo ($sub_page == 40) ? 'active' : ''; ?>">
                        <a href="withdrawal.php">Withdrawal</a>
                    </li>
                    <li class="<?php echo ($sub_page == 41) ? 'active' : ''; ?>">
                        <a href="pending_withdrawal.php">Pending Withdrawal</a>
                    </li>

                    <!-- <li class="<?php echo ($sub_page == 42) ? 'active' : ''; ?>">
                        <a href="approved_withdrawal.php">Approved Withdrawal</a>
                    </li>
                    <li class="<?php echo ($sub_page == 43) ? 'active' : ''; ?>">
                        <a href="rejected_withdrawal.php">Rejected Withdrawal</a>
                    </li> -->

                </ul>
            </li>


            <!-- <li class="menu single-menu <?php echo ($page == 5) ? 'active' : ''; ?>">
                <a href="payment_setting.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Payment Setting</span>
                    </div>
                </a>
            </li> -->



            <li class="menu single-menu <?php echo ($page == 6) ? 'active' : ''; ?>">
                <a href="#app4" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Game Setting</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app4" data-parent="#topAccordion">
                    <li class="<?php echo ($sub_page == 61) ? 'active' : ''; ?>">
                        <a href="level.php">Levels</a>
                    </li>


                    <!-- <li class="<?php echo ($sub_page == 62) ? 'active' : ''; ?>">
                        <a href="invitation_task.php">Invitation Task</a>
                    </li>

                    <li class="<?php echo ($sub_page == 63) ? 'active' : ''; ?>">
                        <a href="activity_task.php">Activity Task</a>
                    </li> -->

                    <!-- <li class="<?php echo ($sub_page == 64) ? 'active' : ''; ?>">
                        <a href="rejected_withdrawal.php">Rejected Withdrawal</a>
                    </li> -->

                    <li class="<?php echo ($sub_page == 65) ? 'active' : ''; ?>">
                        <a href="popup.php">Popup</a>
                    </li>

                    <li class="<?php echo ($sub_page == 66) ? 'active' : ''; ?>">
                        <a href="salary.php">Salary Plans</a>
                    </li>
                    <li class="<?php echo ($sub_page == 67) ? 'active' : ''; ?>">
                        <a href="user_salary.php">User Salary</a>
                    </li>
                </ul>
            </li>


            <!-- <li class="menu single-menu 
                <?php if ($page == 6) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="level.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Levels</span>
                    </div>
                </a>
            </li> -->


            <li class="menu single-menu <?php echo ($page == 7) ? 'active' : ''; ?>">
                <a href="game_setting.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Game Result Setting</span>
                    </div>
                </a>
            </li>



            <li class="menu single-menu <?php echo ($page == 8) ? 'active' : ''; ?>">
                <a href="#app3" aria-expanded="false" class="dropdown-toggle" data-toggle="collapse">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Manual Result</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app3" data-parent="#topAccordion">

                    <li class="<?php echo ($sub_page == 81) ? 'active' : ''; ?>">
                        <!-- <a href="wingo_manual_result.php">Wingo</a> -->
                        <a href="wingo_manual_result.php">Colour Prediction</a>
                    </li>
                    <!-- <li class="<?php echo ($sub_page == 81) ? 'active' : ''; ?>">
                        <a href="pending_recharge.php">Pending Recharge</a>
                    </li> -->

                    <!-- <li class="<?php echo ($sub_page == 82) ? 'active' : ''; ?>">
                        <a href="approved_recharge.php">Approved Recharge</a>
                    </li>
                    <li class="<?php echo ($sub_page == 83) ? 'active' : ''; ?>">
                        <a href="rejected_recharge.php">Rejected Recharge</a>
                    </li> -->

                </ul>
            </li>


            <li class="menu single-menu <?php echo ($page == 9) ? 'active' : ''; ?>">
                <a href="banks.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Banks</span>
                    </div>
                </a>
            </li>



            <li class="menu single-menu 
                <?php if ($page == 10) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="#app4" aria-expanded="false" class="dropdown-toggle" data-toggle="collapse">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <!-- <span>Game History</span> -->
                        <span>Game History</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app4" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 101) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="wingo_bet_history.php">Color Prediction</a>
                    </li>
                    <li class="<?php if ($sub_page == 102) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="aviator_bet_history.php">Aviator</a>
                    </li>

                    <!-- <li class="<?php if ($sub_page == 103) {
                                        echo 'active';
                                    } else {
                                        echo '';
                                    } ?>">
                        <a href="transaction_history.php">Transaction History</a>
                    </li> -->
                </ul>

            </li>


            <li class="menu single-menu <?php echo ($page == 7) ? 'active' : ''; ?> d-none">
                <a href="orders.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Orders</span>
                    </div>
                </a>
            </li>








            <!-- 
            <li class="menu single-menu 
                <?php if ($page == 5) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="blogs.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Blogs</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 51) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="blogs.php">Blogs </a>
                    </li>
                    <li class="<?php if ($sub_page == 52) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="comments.php">Commetns </a>
                    </li>
                </ul>

            </li> -->



            <!-- <li class="menu single-menu <?php echo ($page == 7) ? 'active' : ''; ?>">
                <a href="locations.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Locations</span>
                    </div>
                </a>
            </li>


            <li class="menu single-menu <?php echo ($page == 8) ? 'active' : ''; ?>">
                <a href="notification.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Notification</span>
                    </div>

                </a>
            </li> -->



            <li class="menu single-menu 
                <?php if ($page == 11) {
                    echo 'active';
                } else {
                    echo '';
                } ?>">
                <a href="#app4" aria-expanded="false" class="dropdown-toggle" data-toggle="collapse">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <!-- <span>Game History</span> -->
                        <span>Gift</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app4" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 111) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="gift.php">Gift</a>
                    </li>
                    <li class="<?php if ($sub_page == 112) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="gift_history.php">Gift History</a>
                    </li>
                </ul>

            </li>

            <!-- <li class="menu single-menu <?php echo ($page == 12) ? 'active' : ''; ?>">
                <a href="popup.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Popup</span>
                    </div>

                </a>
            </li> -->


            <!-- <li class="menu single-menu <?php echo ($page == 11) ? 'active' : ''; ?>">
                <a href="gift.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Gift</span>
                    </div>

                </a>
            </li> -->


        </ul>
    </nav>
</div>