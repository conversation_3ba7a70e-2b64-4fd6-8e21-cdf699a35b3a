<?php
// include("../lib/db_connection_cron.php");
include("../lib/db_connection.php");
include("../lib/salary_functions.php");

// $today = date('Y-m-d', strtotime("-5 min"));
// $today = date('Y-m-d', strtotime("-4 day"));


if (isset($_REQUEST['date']) && $_REQUEST['date'] != '') {
    $date = date('Y-m-d', strtotime($_REQUEST['date']));
} else {
    echo "Please select a valid date.";
}
if (isset($_REQUEST['limit']) && $_REQUEST['limit'] != '') {
    $limit = $_REQUEST['limit'];
} else {
    $limit = 50;
}

$userId = '';
if (isset($_REQUEST['user_id']) && $_REQUEST['user_id'] != '') {
    $userId = $_REQUEST['user_id'];
}


// Clear the table before inserting new data
dbQuery("TRUNCATE TABLE tabl_user_data_admin");

// MAX(ws.id) AS wallet_summary_id, -- Ensuring that the wallet summary ID is selected properly

$combined_query = "SELECT 
        u.id, 
        u.own_code, 
        COALESCE(MAX(ws.id), 0) AS wallet_summary_id,  -- Handling NULL values for ws.id
        COALESCE(SUM(b.amount), 0) AS total_trade
    FROM 
        tabl_user u
    LEFT JOIN 
        tabl_walletsummery ws 
    ON 
        u.id = ws.user_id 
        AND ws.actiontype = 'salary_income' 
        AND DATE(ws.date) = '$date'
    LEFT JOIN 
        tabl_wingonew_betting b 
    ON 
        u.id = b.user_id 
        AND DATE(b.date) = '$date'
    WHERE 
        u.status = '1'
    GROUP BY 
        u.id, u.own_code
    HAVING 
        total_trade > 0
";


$sel_user = dbQuery($combined_query);
while ($res_user = dbFetchAssoc($sel_user)) {
    $id = $res_user['id'];
    $own_code = $res_user['own_code'];
    $total_trade = $res_user['total_trade'];
    $wallet_summary_id = $res_user['wallet_summary_id'];

    $status = 0;
    if ($wallet_summary_id != 0) {
        $status = 1;
    }

    dbQuery("INSERT INTO tabl_user_data_admin (id, own_code, total_trade, wallet_summary_id, `status`, `date`) VALUES ('$id', '$own_code', '$total_trade', '$wallet_summary_id', '$status', '$date')");
}


// echo "Active users fetched and stored.";
?>

<table id="zero-config" class="table table-hover" style="width:100%">
    <thead>
        <tr>
            <th>ID</th>
            <th>User ID</th>
            <th>User Code</th>
            <th>Total Trade</th>
            <th>Direct Members</th>
            <th>Indirect Members</th>
            <th>Depositor</th>
            <th>Total Deposit</th>
            <th>Salary</th>
            <th>Action</th>

        </tr>
    </thead>
    <tbody>
        <?php

        // $sel = dbQuery("SELECT * FROM tabl_salary ORDER BY `date`");
        if ($userId == '') {
            $sel = dbQuery("SELECT * FROM tabl_user_data_admin WHERE wallet_summary_id = 0 AND `status`=0 AND `total_trade`>=500 AND `date`='$date' LIMIT $limit");
        } else {
            // $sel = dbQuery("SELECT * FROM tabl_user_data_admin WHERE wallet_summary_id = 0 AND `status`=0 AND `total_trade`>=500 AND `date`='$date' AND (id='$userId' OR own_code='$userId') LIMIT $limit");

            $sel = dbQuery("SELECT * FROM tabl_user_data_admin WHERE wallet_summary_id = 0 AND `status`=0 AND `date`='$date' AND (id='$userId' OR own_code='$userId') LIMIT $limit");
        }
        $i = 1;
        while ($res = dbFetchAssoc($sel)) {
            $user_id = $res['id'];
            $own_code = $res['own_code'];
            $total_trade = $res['total_trade'];

            // Fetch subordinates
            $direct_list = getDirectSubordinates($own_code, $date); // Using updated function
            // $indirect_list = getIndirectSubordinates($own_code, $today); // Using updated function
            $indirect_list = getAllSubordinates($own_code, $date); // Using updated function

            // Calculate salary using the fetched subordinate lists
            // $salary_data = computeSalary($user_id, $total_trade, $direct_list, $indirect_list, $date);

            if (SALARY_SETTING == 1) {
                // Calculate salary using the fetched subordinate lists
                $salary_data = computeSalary($user_id, $total_trade, $direct_list, $indirect_list, $date);
            } else if (SALARY_SETTING == 2) {
                // Calculate salary using the fetched subordinate lists
                $salary_data = computeSalaryPer($direct_list, $indirect_list, $date);
            }
        ?>
            <tr>
                <td>
                    <?php echo $i; ?>
                </td>
                <td>
                    <?php echo $res['id']; ?>
                </td>
                <td>
                    <?php echo $res['own_code']; ?>
                </td>
                <td>
                    <?php echo $res['total_trade']; ?>
                </td>
                <td>
                    <?php echo $salary_data['direct_count']; ?>
                </td>
                <td>
                    <?php echo $salary_data['indirect_count']; ?>
                </td>
                <td>
                    <?php echo $salary_data['depositors_count']; ?>
                </td>
                <td>
                    <?php echo $salary_data['total_deposit']; ?>
                </td>
                <td>
                    <input type="number" class="form-control" style="min-width: 150px" value="<?= $salary_data['amount']; ?>" id="salary_amount_<?= $user_id; ?>">
                </td>

                <td>
                    <button class="btn btn-success" onclick="GiveSalary(this, '<?= $res['id']; ?>', '<?= $salary_data['salary_id']; ?>', '<?= $date; ?>');">
                        Give Salary Now
                    </button>
                </td>

            </tr>
        <?php
            $i++;
        }
        ?>

    </tbody>
</table>

<button class="btn btn-primary" id="salary_btn" onclick="SubmitAllSalaries('<?= $date; ?>');">Submit All Salaries</button>