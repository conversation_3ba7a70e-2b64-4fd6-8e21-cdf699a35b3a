<?php
session_start();
include ('../lib/db_connection.php');

date_default_timezone_set("Asia/Kolkata");
$date = date("Y-m-d H:i:s");

if (isset($_REQUEST['val'])) {
    $table_name = $_REQUEST['tabl'];
    $user_id = $_REQUEST['user_id'];

    if ($_REQUEST['val'] == 1) {
        $sel3 = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $_REQUEST['user_id'] . "',order_id='" . $_REQUEST['row_id'] . "',amount='" . $_REQUEST['amount'] . "',type='credit',actiontype='recharge~" . $_REQUEST['row_id'] . "',date='" . $date . "' ");
        update_wallet($_REQUEST['user_id'], $_REQUEST['amount'], 'credit');

        $status = dbQuery("UPDATE $table_name SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['row_id'] . "'");

        dbQuery("INSERT INTO `tabl_notification` SET `user_id`='" . $user_id . "', `type`='recharge', `title`='recharge approved', `description`='Your account has arrived ₹" . $_REQUEST['amount'] . "', data='" . $_REQUEST['amount'] . "',`status`='0', `date`='$date'");

    } else {
        $status = dbQuery("UPDATE $table_name SET status='" . $_REQUEST['val'] . "' WHERE id='" . $_REQUEST['row_id'] . "'");

        dbQuery("INSERT INTO `tabl_notification` SET `user_id`='" . $user_id . "', `type`='recharge', `title`='recharge rejected', `description`='Your recharge request has been rejected', data='" . $_REQUEST['amount'] . "', `status`='0', `date`='$date'");
    }

    if ($status) {
        echo '1';
    }
}
