<div class="topbar-nav header navbar" role="banner">
    <nav id="topbar">
        <ul class="navbar-nav theme-brand flex-row  text-center">
            <li class="nav-item theme-text">
                <a href="home.php" class="nav-link"> <?php echo SITE; ?> </a>
            </li>
        </ul>

        <ul class="list-unstyled menu-categories" id="topAccordion">

            <li class="menu single-menu <?php if ($page == 1) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="home.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-home">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9 22 9 12 15 12 15 22"></polyline>
                        </svg>
                        <span>Home</span>
                    </div>

                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 2) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Enqueries</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 21) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="popupdata.php">Popup Form </a>
                    </li>
                    <li class="<?php if ($sub_page == 22) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="loan.php">Loan</a>
                    </li>

                    <li class="<?php if ($sub_page == 23) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="postbyreq.php">Post By Requirments</a>
                    </li>

                    <li class="<?php if ($sub_page == 24) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="contactus.php"> Contact Us </a>
                    </li>

                    <li class="<?php if ($sub_page == 25) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="socialadds.php"> Social Add </a>
                    </li>

                    <li class="<?php if ($sub_page == 26) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="partnerform.php"> Partner From </a>
                    </li>


                    <li class="<?php if ($sub_page == 31) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2b">B2B </a>
                    </li>
                    <li class="<?php if ($sub_page == 31) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2b">B2B </a>
                    </li>
                    <li class="<?php if ($sub_page == 32) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2b2c">B2B2C</a>
                    </li>

                    <li class="<?php if ($sub_page == 33) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2c">B2C</a>
                    </li>
                    <li class="<?php if ($sub_page == 34) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2g">B2G</a>
                    </li>

                </ul>
            </li>

            <li class="menu single-menu <?php if ($page == 3) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="business_enquiry.php" aria-expanded="false" class="dropdown-toggle">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Business Enquiry</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 31) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2b">B2B </a>
                    </li>
                    <li class="<?php if ($sub_page == 32) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2b2c">B2B2C</a>
                    </li>

                    <li class="<?php if ($sub_page == 33) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2c">B2C</a>
                    </li>
                    <li class="<?php if ($sub_page == 34) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="business_enquiry.php?type=b2g">B2G</a>
                    </li>

                </ul>
            </li>

            <li class="menu single-menu <?php if ($page == 4) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="user.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>User</span>
                    </div>

                </a>
            </li>

            
            
            <!-- <li class="menu single-menu <?php if ($page == 5) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="promo_code.php">
                            <div class="">
                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Promo Code</span>
                            </div>

                        </a>                        
                    </li> -->
            <!-- <li class="menu single-menu <?php if ($page == 7) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="orders.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Orders</span>
                            </div>

                        </a>                        
                    </li> -->

            <!-- <li class="menu single-menu <?php if ($page == 8) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                            <div class="">
                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-image"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>
                                <span>Banners</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </a>
                        <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                            <li class="<?php if ($sub_page == 81) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="main_slider.php"> Main Slider </a>
                            </li>  
                            <li class="<?php if ($sub_page == 82) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="single_banner.php"> Single Banners </a>
                            </li>  
                            <li class="<?php if ($sub_page == 83) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="small_banner3.php"> 3 Small Banners </a>
                            </li> 
                            <li class="<?php if ($sub_page == 84) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="big_banner2.php"> 2 Big Banners </a>
                            </li> 
                            <li class="<?php if ($sub_page == 85) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="small_banner2.php"> 2 Small Banners </a>
                            </li>                             
                        </ul>
                    </li> -->


            <li class="menu single-menu <?php if ($page == 5) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="blogs.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Blogs</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 51) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="blogs.php">Blogs </a>
                    </li>
                    <li class="<?php if ($sub_page == 52) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="comments.php">Commetns </a>
                    </li>
                </ul>

            </li>



            <li class="menu single-menu <?php if ($page == 6) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="main_category.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Category</span>
                    </div>

                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 7) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="locations.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Locations</span>
                    </div>

                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 8) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="plans.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Plans</span>
                    </div>

                </a>
            </li>

            <li class="menu single-menu <?php if ($page == 9) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                <a href="vendor.php">
                    <div class="">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path>
                            <polyline points="13 2 13 9 20 9"></polyline>
                        </svg>
                        <span>Vendors</span>
                    </div>

                </a>
                <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                    <li class="<?php if ($sub_page == 91) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="vendor.php">All Vendors </a>
                    </li>
                    <li class="<?php if ($sub_page == 92) {
                                    echo 'active';
                                } else {
                                    echo '';
                                } ?>">
                        <a href="vendor_approval.php">Vendors Approval</a>
                    </li>
                </ul>

            </li>

            


            <!--
					
                    
					
					<li class="menu single-menu <?php if ($page == 6) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="#app" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                            <div class="">
                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Shop</span>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>
                        </a>
                        <ul class="collapse submenu list-unstyled" id="app" data-parent="#topAccordion">
                            <li class="<?php if ($sub_page == 61) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="shop_category.php"> Shop Category </a>
                            </li> 
							<li class="<?php if ($sub_page == 62) {
                                            echo 'active';
                                        } else {
                                            echo '';
                                        } ?>">
                                <a href="products.php"> Apk </a>
                            </li> 
                        
                        </ul>
                    </li>
                    
                   <li class="menu single-menu <?php if ($page == 7) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="orders.php">
                            <div class="">
                                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Orders</span>
                            </div>

                        </a>                        
                    </li>
            -->
            <!--  <li class="menu single-menu <?php if ($page == 3) {
                                                    echo 'active';
                                                } else {
                                                    echo '';
                                                } ?>">
                        <a href="home.php">
                            <div class="">
                               <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                                <span>Instructor</span>
                            </div>

                        </a>                        
                    </li>-->

        </ul>
    </nav>
</div>