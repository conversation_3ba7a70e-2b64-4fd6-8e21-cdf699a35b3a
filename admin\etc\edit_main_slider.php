<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 8;
$sub_page = 81;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
if (isset($_REQUEST['submit'])) {
    if ($_FILES["image"]["name"] != "") {
        $target_dir = "../assets/images/slider/";
        $name = rand(10000, 1000000);
        $extension = pathinfo($_FILES["image"]["name"], PATHINFO_EXTENSION);
        $new_name = $name . "." . $extension;
        $target_file = $target_dir . $name . "." . $extension;

        $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
        if ($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg") {
            die("This is not valid image. Please try again.");
        } else {
            move_uploaded_file($_FILES["image"]["tmp_name"], $target_file);
            $target_path = "../assets/images/slider/" . $new_name;
            $resizeObj = new resize("../assets/images/slider/" . $new_name);
            $resizeObj->resizeImage(100, 60, 'exact');
            $resizeObj->saveImage("../assets/images/slider/thumb-100/" . $new_name, 100);

            $resizeObj = new resize("../assets/images/slider/" . $new_name);
            $resizeObj->resizeImage(1313, 500, 'exact');
            $resizeObj->saveImage("../assets/images/slider/thumb-1300/" . $new_name, 100);

            dbQuery("UPDATE tabl_main_slider SET image='" . $new_name . "',date_added='" . $date . "' WHERE  id='" . $_REQUEST['id'] . "'");
        }
    } else {
        //dbQuery("UPDATE tabl_main_category SET name='".mysqli_real_escape_string($con,$_REQUEST['name'])."',date_added='".$date."' WHERE  id='".$_REQUEST['id']."'");

    }
    echo '<script>alert("Main-Slider Updated!");window.location.href="main_slider.php"</script>';
}
$sel = dbQuery("SELECT * FROM tabl_main_slider WHERE id='" . $_REQUEST['id'] . "'");
$res = dbFetchAssoc($sel);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no">
    <title><?php echo SITE; ?> - Edit Main Slider </title>
    <link rel="icon" type="image/x-icon" href="assets/img/Grocery_logo.jpg" />
    <link href="assets/css/loader.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/loader.js"></script>

    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="https://fonts.googleapis.com/css?family=Quicksand:400,500,600,700&amp;display=swap" rel="stylesheet">
    <link href="bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/plugins.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM STYLES -->
    <link href="plugins/apex/apexcharts.css" rel="stylesheet" type="text/css">
    <link href="assets/css/dashboard/dash_2.css" rel="stylesheet" type="text/css" />
    <!-- END PAGE LEVEL PLUGINS/CUSTOM STYLES -->

    <link rel="stylesheet" type="text/css" href="plugins/dropify/dropify.min.css">
    <link href="assets/css/users/account-setting.css" rel="stylesheet" type="text/css" />

    <link href="assets/css/elements/miscellaneous.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/elements/breadcrumb.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.ckeditor.com/4.16.0/standard/ckeditor.js"></script>

    <style>
        .image {
            max-width: 100px;
            max-height: 100px;
        }
    </style>
</head>

<body class="alt-menu sidebar-noneoverflow">

    <?php include('inc/__header.php'); ?>
    <!--  BEGIN MAIN CONTAINER  -->
    <div class="main-container" id="container">

        <div class="overlay"></div>
        <div class="search-overlay"></div>

        <!--  BEGIN TOPBAR  -->
        <?php include('inc/__menu.php'); ?>
        <!--  END TOPBAR  -->

        <!--  BEGIN CONTENT PART  -->
        <div id="content" class="main-content">
            <div class="layout-px-spacing">
                <nav class="breadcrumb-one" aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="main_slider.php">Main Slider</a></li>
                        <li class="breadcrumb-item active"><a href="javascript:void(0);">Edit</a></li>

                    </ol>
                </nav>
                <div class="account-settings-container layout-top-spacing">

                    <div class="account-content">
                        <div class="scrollspy-example" data-spy="scroll" data-target="#account-settings-scroll" data-offset="-100">
                            <div class="row">
                                <div class="col-xl-12 col-lg-12 col-md-12 layout-spacing">
                                    <form name="account" method="post" id="general-info" class="section general-info" enctype="multipart/form-data">

                                        <div class="info">
                                            <h6 class="">Edit Main Slider</h6>
                                            <div class="row">
                                                <div class="col-lg-11 mx-auto">
                                                    <div class="row">
                                                        <div class="col-xl-10 col-lg-12 col-md-8 mt-md-0 mt-4">
                                                            <div class="form">
                                                                <div class="row">
                                                                    <!--   
                                                                 <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Name</label>
                                                                            <input type="text" class="form-control mb-4" id="Name" name="name" value="<?php echo $res['name']; ?>" placeholder="Name" required>
                                                                        </div>
                                                                        
                                                                    </div>
										
										<div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Description</label>
                                                                            <textarea class="form-control mb-4" id="description" name="description" placeholder="Description" rows="5" required><?php echo $res['description']; ?></textarea>
                                                                        </div>
                                                                        
                                                                    </div>-->

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <img class="image" src="../assets/images/slider/thumb-100/<?php echo $res['image']; ?>">
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName">Update Image</label>
                                                                            <input type="file" class="form-control mb-4" id="Name" name="image" placeholder="Image">
                                                                        </div>
                                                                    </div>



                                                                    <div class="col-sm-12">
                                                                        <div class="form-group">
                                                                            <label for="fullName"></label>
                                                                            <button type="submit" name="submit" class="btn btn-secondary mb-4 mr-2">Update</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php include('inc/__footer.php'); ?>
            </div>

        </div>
        <!--  END CONTENT PART  -->

    </div>
    <!-- END MAIN CONTAINER -->

    <!-- BEGIN GLOBAL MANDATORY SCRIPTS -->
    <script src="assets/js/libs/jquery-3.1.1.min.js"></script>
    <script src="bootstrap/js/popper.min.js"></script>
    <script src="bootstrap/js/bootstrap.min.js"></script>
    <script src="plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>
    <script src="assets/js/app.js"></script>
    <script>
        $(document).ready(function() {
            App.init();
        });
    </script>
    <script src="assets/js/custom.js"></script>
    <!-- END GLOBAL MANDATORY SCRIPTS -->

    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->
    <script src="plugins/apex/apexcharts.min.js"></script>
    <script src="assets/js/dashboard/dash_2.js"></script>
    <!-- BEGIN PAGE LEVEL PLUGINS/CUSTOM SCRIPTS -->

    <script src="plugins/dropify/dropify.min.js"></script>
    <script src="plugins/blockui/jquery.blockUI.min.js"></script>
    <!-- <script src="plugins/tagInput/tags-input.js"></script> -->
    <script src="assets/js/users/account-settings.js"></script>
</body>

</html>
<script>
    CKEDITOR.replace('description');
</script>