.main-container {
    min-height: auto;
}
.layout-px-spacing {
    min-height: auto!important;
}
@keyframes fadeInUp {
    from {
        transform: translate3d(0,40px,0)
    }

    to {
        transform: translate3d(0,0,0);
        opacity: 1
    }
}
@-webkit-keyframes fadeInUp {
    from {
        transform: translate3d(0,40px,0)
    }

    to {
        transform: translate3d(0,0,0);
        opacity: 1
    }
}
.animated {
    animation-duration: 1s;
    animation-fill-mode: both;
    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: both
}
.animatedFadeInUp {
    opacity: 0
}
.fadeInUp {
    opacity: 0;
    animation-name: fadeInUp;
    -webkit-animation-name: fadeInUp;
}
.actions-btn-tooltip.tooltip {
    opacity: 1;
    top: -11px!important;
}
.actions-btn-tooltip .arrow:before {
    border-top-color: #3b3f5c;
}
.actions-btn-tooltip .tooltip-inner {
    background: #3b3f5c;
    color: #fff;
    font-weight: 700;
    border-radius: 30px;
    box-shadow: 0px 5px 15px 1px rgba(113, 106, 202, 0.2);
    padding: 4px 16px;
}
.doc-container {
    position: relative;
    display: flex;
    background: #fff;
    border-radius: 6px;
    -webkit-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
    -moz-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
    box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
    margin-bottom: 10px;
}
.tab-title {
    min-width: 250px;
}
.tab-title .search {
    border-bottom: 1px solid #e0e6ed;
    border-right: 1px solid #e0e6ed;
}
.tab-title .search input {
    border: none;
    padding: 18px 18px 18px 18px;
    background: transparent;
    height: auto;
}
.tab-title .search input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: #888ea8;
}
.tab-title .search input::-moz-placeholder { /* Firefox 19+ */
  color: #888ea8;
}
.tab-title .search input:-ms-input-placeholder { /* IE 10+ */
  color: #888ea8;
}
.tab-title .search input:-moz-placeholder { /* Firefox 18- */
  color: #888ea8;
}
.tab-title .inv-list-container {
    position: relative;
    margin: auto;
    overflow: auto;  
    border-right: 1px solid #e0e6ed;
    height: calc(100vh - 256px);
}
.tab-title .nav-item {
    border-bottom: 1px solid #e0e6ed;
}
.tab-title .list-actions {
    padding: 20px 10px;
    border-radius: 5px;
    transition: all 0.35s ease;
    cursor: pointer;
}
.tab-title .list-actions.active {
    background-color: #fff;
    -webkit-transform: translateY(0) scale(1.03);
    transform: translateY(0) scale(1.03);
    box-shadow: 0px 0px 15px 1px #ebedf2;
}
.tab-title .list-actions:hover {
    -webkit-transform: translateY(0) scale(1.03);
    transform: translateY(0) scale(1.03);
    box-shadow: 0px 0px 15px 1px #ebedf2;
}
.tab-title .list-actions .f-m-body {
    display: flex;
}
.tab-title .list-actions .f-m-body .f-head {
    margin-right: 13px;
    align-self: center;
}
.tab-title .list-actions .f-m-body .f-head svg {
    background: rgba(0, 23, 55, 0.08);
    border-radius: 50%;
    padding: 6px;
    color: #0e1726;
    width: 30px;
    height: 30px;
}
.tab-title .list-actions .f-m-body .f-body {}
.tab-title .list-actions .f-m-body .f-body .invoice-number {
    margin-bottom: 0;
    font-size: 12px;
    color: #888ea8;
    font-weight: 600;
}
.tab-title .list-actions .f-m-body .f-body .invoice-user-name {
    font-size: 14px;
    font-weight: 700;
    color: #0e1726;
    margin-bottom: 0;
}
.tab-title .list-actions .f-m-body .f-body .invoice-user-name span {
    color: #3b3f5c;
}
.tab-title .list-actions .f-m-body .f-body .invoice-generated-date {
    margin-bottom: 0;
    font-size: 13px;
    font-weight: 600;
    color: #3b3f5c;
}
.invoice-container {
    width: 100%;
}
.invoice-inbox {
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    max-width: 100%;
    width: 100%;
    height: calc(100vh - 197px);
}
.invoice-inbox .inv-not-selected {
    display: flex;
    height: 100%;
    justify-content: center;
    background-image: url(../../img/bg.png);
    border-bottom-right-radius: 6px;
    border-top-right-radius: 6px;
}
.invoice-inbox .inv-not-selected p {
    align-self: center;
    font-size: 18px;
    color: #3b3f5c;
    margin-bottom: 0;
    font-weight: 600;
    background: #bfc9d4;
    padding: 7px 11px;
    border-radius: 6px;
    -webkit-box-shadow: 0px 2px 4px rgba(126,142,177,0.12);
    box-shadow: 0px 2px 4px rgba(126,142,177,0.12);
}
.invoice-inbox .invoice-header-section {
    display: flex;
    justify-content: space-between;
    padding: 17px 20px;
    border-bottom: 1px solid #ebedf2;
    display: none;
}
.invoice-inbox .inv-number {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 0;
    color: #888ea8;
}
.invoice-inbox .invoice-action svg {
    cursor: pointer;
    font-weight: 600;
    color: #888ea8;
    margin-right: 6px;
    vertical-align: middle;
    fill: rgba(0, 23, 55, 0.08);
}
.invoice-inbox .invoice-action svg:not(:last-child) {
    margin-right: 15px;
}
.invoice-inbox .invoice-action svg:hover {
    color: #1b55e2;
    fill: rgba(27, 85, 226, 0.23921568627450981);
}
.invoice-inbox #ct {
    display: none;
}


/*
===================

     Invoice

===================
*/

/*Invoice*/

.content-section {
    padding: 36px 35px;
}
.invoice .content-section {
    height: calc(100vh - 185px);
}

/*    Inv head section   */

.invoice .content-section .inv--head-section {
    margin-bottom: 50px;
}
.invoice .content-section .inv--head-section h3.in-heading {
    font-size: 32px;
    font-weight: 700;
    color: #0e1726;
    margin: 0;
}
.invoice .content-section .inv--head-section div.company-info {
    display: flex;
    justify-content: flex-end;
}
.invoice .content-section .inv--head-section div.company-info svg {
    width: 42px;
    height: 42px;
    margin-right: 10px;
    color: #1b55e2;
    fill: rgba(27, 85, 226, 0.23921568627450981);
}
.invoice .content-section .inv--head-section .inv-brand-name {
    font-size: 23px;
    font-weight: 600;
    margin-bottom: 0;
    align-self: center;
}

/*    Inv detail section    */

.invoice .content-section .inv--detail-section .inv-to {
   font-weight: 700;
    font-size: 15px;
    margin-bottom: 15px;
}
.invoice .content-section .inv--detail-section .inv-user-name {
    font-weight: 700;
    margin-bottom: 2px;
    font-size: 13px;
    color: #1b55e2;
}
.invoice .content-section .inv--detail-section .inv-detail-title {
    font-weight: 700;
    margin-bottom: 0;
    font-size: 15px;
    margin-bottom: 15px;
}
.invoice .content-section .inv--detail-section .inv-details {
    font-weight: 700;
    margin-bottom: 15px;
}
.invoice .content-section .inv--detail-section .inv-street-addr {
    font-weight: 600;
    margin-bottom: 2px;
    font-size: 13px;
}
.invoice .content-section .inv--detail-section .inv-email-address {
    font-weight: 600;
    margin-bottom: 2px;
    font-size: 13px;
}

/*inv-list-number*/
.invoice .content-section .inv--detail-section .inv-list-number {
    margin-bottom: 2px;
}
.invoice .content-section .inv--detail-section .inv-list-number .inv-title {
    font-weight: 700;
    font-size: 13px;
}
.invoice .content-section .inv--detail-section .inv-list-number .inv-number {
    font-weight: 700;
    font-size: 13px;
    color: #1b55e2;
}

/*inv-created-date*/
.invoice .content-section .inv--detail-section .inv-created-date {
    margin-bottom: 2px;
}
.invoice .content-section .inv--detail-section .inv-created-date .inv-title {
    font-weight: 700;
    font-size: 13px;
}
.invoice .content-section .inv--detail-section .inv-created-date .inv-date {
    font-size: 13px;
    font-weight: 600;
}

/*inv-due-date*/
.invoice .content-section .inv--detail-section .inv-due-date {
    margin-bottom: 2px;
}
.invoice .content-section .inv--detail-section .inv-due-date .inv-title {
    font-weight: 700;
    font-size: 13px;
}
.invoice .content-section .inv--detail-section .inv-due-date .inv-date {
    font-size: 13px;
    font-weight: 600;
}

/*    Inv product table section    */
.invoice .content-section .inv--product-table-section {
    margin-top: 50px;
    margin-bottom: 50px;
}
.invoice .content-section .inv--product-table-section table {
    border: 1px solid #e0e6ed;
}
.invoice .content-section .inv--product-table-section thead tr {
    border: none;
    background: #ebedf2;
}
.invoice .content-section .inv--product-table-section th {
    border: none;
    color: #1b55e2;
}
.invoice .content-section .inv--product-table-section td {
    border-top: 1px solid #e0e6ed;
    color: #515365;
    font-weight: 600;
}

/*inv--payment-info*/
.invoice .content-section .inv--payment-info {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 52px;
}
.invoice .content-section .inv--payment-info .inv-title {
    color: #1b55e2;
    font-weight: 600;
    margin-bottom: 15px;
}
.invoice .content-section .inv--payment-info .inv-subtitle {
    font-weight: 700;
    font-size: 14px;
}

/*inv--total-amounts*/
.invoice .content-section .inv--total-amounts {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 52px;
}
.invoice .content-section .inv--total-amounts .grand-total-title h4 {
    position: relative;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 0;
    padding: 0;
    color: #0e1726;
    display: inline-block;
    letter-spacing: 1px;
}
.invoice .content-section .inv--total-amounts .grand-total-amount h4 {
    position: relative;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 0;
    padding: 0;
    color: #0e1726;
    display: inline-block;
    letter-spacing: 1px;
}

/*inv--thankYou*/
.inv--thankYou {
    display: none;
    text-align: center;
}
.inv--thankYou p {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 12px;
    color: #888ea8;
}

@media print {
  body * {
    visibility: hidden;
  }
  #ct, #ct * {
    visibility: visible;
  }
  .doc-container {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
  }
}

@page { size: auto;  margin: 0mm; }
@media (max-width: 1199px) {
    .doc-container { overflow: auto; }
    .tab-title {
        position: absolute;
        z-index: 60;
        left: -300px;
    }
    .tab-title.open-inv-sidebar {
        left: 0;
        background: #fff;
    }
    .app-hamburger-container {
        text-align: right;
    }
    .hamburger {
        position: relative;
        top: -13px;
        padding: 6px 9px 6px 9px;
        font-size: 20px;
        color: #fff;
        align-self: center;
        display: inline-block;
        background-color: #515365;
        border-radius: 50%;
    }
}
@media (max-width: 575px) {
    .invoice .content-section .inv--head-section div.company-info {
        justify-content: flex-start;
    }
    .invoice .content-section .inv--detail-section .inv-detail-title {
        margin-top: 20px;
        margin-bottom: 10px;
    }
}
@media (max-width: 991px) {
    .layout-top-spacing {
        margin-top: 37px;
    }
}