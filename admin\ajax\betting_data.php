<?php
session_start();
include('../lib/db_connection.php');

// $userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
// $user = dbFetchAssoc($userq);

date_default_timezone_set("Asia/Kolkata");
$date = date("Y-m-d");

$game_id = $_REQUEST['gameid'];

if($game_id != ''){


for ($i = 0; $i < 10; $i++) {
?>
    <tr>
        <td><?php echo $game_id; ?></td>
        <td><?php echo $i; ?></td>
        <td><?php echo bet_coins($game_id, 'a', $i); ?></td>
        <td><?php echo bet_coins($game_id, 'b', $i); ?></td>
        <td><?php echo bet_coins($game_id, 'c', $i); ?></td>

    </tr>
<?php
}
}

?>