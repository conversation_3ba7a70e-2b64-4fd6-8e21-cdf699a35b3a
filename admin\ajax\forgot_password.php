<?php 
session_start();
include('../lib/db_connection.php');

$sel=dbQuery("SELECT * FROM tabl_admin WHERE email='".$_REQUEST['email']."'");
$num=dbNumRows($sel);
if($num>0){
$res=dbFetchAssoc($sel);

$to = $_REQUEST['email']; // note the comma
// Subject
$subject = 'Recover Password';
// Message
$message ='
<html>
<body>
<table align="center" role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: auto;">
  <tbody>
    <tr>
     				<td class="bg_white logo" style="padding: 1em 2.5em; text-align: center"><h1><img src="https://maddamall.com/x.jpg"></h1>    </td>
    </tr>
    
    <tr>
      <td><div class="text" style="padding: 0 3em; text-align: center;">
          <h2>Thank you for getting in touch! We appreciate you.</h2><br/>
          <h4>Hi '.$res['name'].',<br/>
                Here is your credential details:<br/>
                  Password: '.$res['password'].'
                            </h4>
        </div>
      </td>
    </tr>
  </tbody>
</table>
</body>
</html>';

// To send HTML mail, the Content-type header must be set
$headers[] = 'MIME-Version: 1.0';
$headers[] = 'Content-type: text/html; charset=iso-8859-1';

// Additional headers
$headers[] = 'From: Madda Mall <<EMAIL>>';
// Mail it
mail($to, $subject, $message, implode("\r\n", $headers));

echo 1;
}else{			
echo 2;
}
?>
