<?php
define('HOSTNAME', 'localhost');

define ('USERNAME', 'u587422572_db1');
define ('PASSWORD', '3Vy[2mkn9');
define ('DATABASE_NAME', 'u587422572_db1');

// define('USERNAME', 'root');
// define('PASSWORD', '');
// define('DATABASE_NAME', 'kri_showbaazi');


$con = mysqli_connect(HOSTNAME, USERNAME, PASSWORD, DATABASE_NAME);
if (mysqli_connect_errno()) {
	echo "Failed to connect to MySQL: " . mysqli_connect_error();
}


// date_default_timezone_set('Asia/Kolkata');


function dbQuery($sql)
{
	global $con;
	$result = mysqli_query($con, $sql);
	return $result;
}

function dbAffectedRows()
{
	global $con;
	return mysqli_affected_rows($con);
}

// function dbFetchArray($result, $resultType = MYSQL_NUM) {
// 	return mysqli_fetch_array($result, $resultType);
// }

function dbFetchAssoc($result)
{
	if ($result) {
		return mysqli_fetch_assoc($result);
	}
	return false;
}

function dbFetchRow($result)
{
	return mysqli_fetch_row($result);
}

function dbFreeResult($result)
{
	return mysqli_free_result($result);
}

function dbNumRows($result)
{
	return mysqli_num_rows($result);
}

// function dbSelect($con)
// {
// 	return mysqli_select_db($con);
// }

function dbInsertId()
{
	global $con;
	return mysqli_insert_id($con);
}

$setting = dbQuery("SELECT * FROM tabl_setting WHERE id=1");
$res_setting = dbFetchAssoc($setting);

define("SITE", $res_setting['site_name']);
define("EMAIL", $res_setting['site_email']);
define("MIN_RECHARGE", $res_setting['min_recharge']);
define("MIN_WITHDRAW", $res_setting['min_withdraw']);
define("USDT_RATE", $res_setting['usdt_to_inr_rate']);
define("INVITE_BONUS", $res_setting['invite_bonus']);
define("SELF_BONUS", $res_setting['self_bonus']);
define("LEVEL_1_COMM", $res_setting['level_1_comm']);
define("LEVEL_2_COMM", $res_setting['level_2_comm']);
define("LEVEL_3_COMM", $res_setting['level_3_comm']);

function update_wallet($user_id, $amount, $type)
{

	if ($type == 'credit') {
		$amount_new = check_wallet($user_id) + $amount;
	} else if ($type == 'debit') {
		$amount_new = check_wallet($user_id) - $amount;
	}
	$sel2 = dbQuery("UPDATE tabl_wallet SET amount='" . $amount_new . "' WHERE user_id='" . $user_id . "'");
}

function check_wallet($user_id)
{
	$sel = dbQuery("SELECT * FROM tabl_wallet WHERE user_id='" . $user_id . "'");
	$res = dbFetchAssoc($sel);

	return $res['amount'];
}


function update_wallet_balance($user_id, $amount)
{

	$amount_new = $amount;

	$sel2 = dbQuery("UPDATE tabl_wallet SET amount='" . $amount_new . "' WHERE user_id='" . $user_id . "'");

	return $sel2;
}







function invitebonus1($user_id, $refcode)
{
	$chksummery = dbQuery("SELECT * FROM `tabl_walletsummery` WHERE `user_id`='$user_id' and `actiontype`='recharge'");
	$chksummeryRow = dbNumRows($chksummery);
	if ($chksummeryRow == '1') {
		$userQuery = dbQuery("SELECT `id` from `tabl_user` WHERE `owncode`='$refcode'");
		$userResult = dbFetchAssoc($userQuery);
		$refuserid = $userResult['id'];
		$selectwallet = dbQuery("SELECT `amount` from `tabl_bonus` WHERE `user_id`='" . $refuserid . "'");
		$walletResult = dbFetchAssoc($selectwallet);
		$availableBalance = $walletResult['amount'];

		$sqlbonus = dbQuery("SELECT `bonusamount` from `tabl_paymentsetting` WHERE `id`='1'");
		$bonusResult = dbFetchAssoc($sqlbonus);
		$bonusAmount = $bonusResult['bonusamount'];
		$finalbonusbalance = $availableBalance + $bonusAmount;
		$today = date("Y-m-d H:i:s");

		$sqlbonuslevel1 = dbQuery("UPDATE `tabl_bonus` SET `amount` = '" . $finalbonusbalance . "',`level1` = '" . $finalbonusbalance . "' WHERE `user_id`= '" . $refuserid . "'");
		$sql = dbQuery("INSERT INTO `tabl_bonussummery`(`user_id`,`periodid`,`level1id`,`level2id`,`level1amount`,`level2amount`,`tradeamount`,`date`) VALUES ('" . $user_id . "','0','" . $refuserid . "','0','110','0','0','" . $today . "')");
	}
}


function userpromocode1($a, $user_id, $code, $tradeamount, $periodid)
{
	$today = date("Y-m-d H:i:s");
	$commissionQuery = mysqli_query($a, "SELECT * FROM `tabl_paymentsetting` WHERE `id`='1'");
	$commissionResult = dbFetchAssoc($commissionQuery);
	$level1commission = $commissionResult['level1'];
	$level2commission = $commissionResult['level2'];
	$level1 = ($tradeamount * $level1commission / 100);
	$level2 = ($tradeamount * $level2commission / 100);

	$userlevel1Query = mysqli_query($a, "SELECT `code`,(select `id` from `tabl_user` WHERE `owncode`='$code')level1id,(select `code` from `tabl_user` WHERE `owncode`='$code')level1code from `tabl_user` WHERE `id`='" . $user_id . "'");
	$userlevel1Result = dbFetchAssoc($userlevel1Query);
	$level1id = $userlevel1Result['level1id'];
	$level1code = $userlevel1Result['level1code'];
	//===============================================================================================
	$userlevel2Query = mysqli_query($a, "SELECT `id` from `tabl_user` WHERE `owncode`='" . $level1code . "'");
	$userlevel2Result = dbFetchAssoc($userlevel2Query);
	$level2id = $userlevel2Result['id'];
	//=================================================================================================
	$sql = mysqli_query($a, "INSERT INTO `tabl_bonussummery`(`user_id`,`periodid`,`level1id`,`level2id`,`level1amount`,`level2amount`,`tradeamount`,`date`) VALUES ('" . $user_id . "','" . $periodid . "','" . $level1id . "','" . $level2id . "','" . $level1 . "','" . $level2 . "','" . $tradeamount . "','" . $today . "')");
	$level1balance = bonus($a, 'level1', $level1id);
	$finallevel1balance = $level1balance + $level1;
	$bonusbalance1 = bonus($a, 'amount', $level1id);
	$finalbonusbalance1 = $bonusbalance1 + $level1;


	$level2balance = bonus($a, 'level2', $level2id);
	$finallevel2balance = $level2balance + $level2;

	$bonusbalance2 = bonus($a, 'amount', $level2id);
	$finalbonusbalance2 = $bonusbalance2 + $level2;


	$sqlbonuslevel1 = mysqli_query($a, "UPDATE `tabl_bonus` SET `amount` = '" . $finalbonusbalance1 . "',`level1` = '" . $finallevel1balance . "' WHERE `user_id`= '" . $level1id . "'");

	$sqlbonuslevel2 = mysqli_query($a, "UPDATE `tabl_bonus` SET `amount` = '" . $finalbonusbalance2 . "',`level2` = '" . $finallevel2balance . "' WHERE `user_id`= '" . $level2id . "'");
}


function getBaseUrl1()
{
	// output: /myproject/index.php
	$currentPath = $_SERVER['PHP_SELF'];

	// output: Array ( [dirname] => /myproject [basename] => index.php [extension] => php [filename] => index ) 
	$pathInfo = pathinfo($currentPath);

	// output: localhost
	$hostName = $_SERVER['HTTP_HOST'];

	// output: http://
	$protocol = strtolower(substr($_SERVER["SERVER_PROTOCOL"], 0, 5)) == 'https://' ? 'https://' : 'http://';

	// return: http://localhost/myproject/
	return $protocol . $hostName . $pathInfo['dirname'] . '/';
}







function check_invites($user_id, $step_no)
{
	$sel_ref_code = dbQuery("SELECT * FROM `tabl_user` WHERE `id`='$user_id'");
	if ($res_ref_code = dbFetchAssoc($sel_ref_code)) {
		$own_code = $res_ref_code['own_code'];
		$sel_user = dbQuery("SELECT COUNT('id') as total_invites from `tabl_user` WHERE `ref_code`='$own_code'");
		if ($res_user = dbFetchAssoc($sel_user)) {
			$sel_task = dbQuery("SELECT * FROM tabl_invitation_task WHERE `step_no`='$step_no'");
			if ($res_task = dbFetchAssoc($sel_task)) {
				if ($res_user['total_invites'] >= $res_task['no_of_invites']) {
					return $res_task['no_of_invites'];
				}
			}
			return $res_user['total_invites'];
		}
	}
	return 0;
}


function check_recharge($user_id, $step_no)
{
	// Fetch the user's own code
	$sel_user = dbQuery("SELECT own_code FROM `tabl_user` WHERE `id`='$user_id'");
	if ($res_user = dbFetchAssoc($sel_user)) {
		$own_code = $res_user['own_code'];

		// Fetch the total number of downlines who have recharged with at least 500
		$sel_downlines = dbQuery("SELECT COUNT(id) as total_downlines FROM `tabl_user` WHERE `ref_code`='$own_code' AND `id` IN (SELECT `user_id` FROM `tabl_deposits` WHERE `amount` >= 500 AND `status`='1')");
		if ($res_downlines = dbFetchAssoc($sel_downlines)) {

			// Fetch the task details
			$sel_task = dbQuery("SELECT * FROM tabl_invitation_task WHERE `step_no`='$step_no'");
			if ($res_task = dbFetchAssoc($sel_task)) {
				// Check if the total downlines meets the task requirement
				if ($res_downlines['total_downlines'] >= $res_task['no_of_invites']) {
					return $res_task['no_of_invites'];
				}
			}
			// Return the total downlines count if the task requirement is not met
			return $res_downlines['total_downlines'];
		}
	}
	// Return 0 if user or downlines not found
	return 0;
}


function check_invite_bonus_eligibility($user_id, $step_no)
{
	// Fetch the task details
	$sel_task = dbQuery("SELECT * FROM tabl_invitation_task WHERE `step_no`='$step_no'");
	if ($res_task = dbFetchAssoc($sel_task)) {

		if (check_invites($user_id, $step_no) >= $res_task['no_of_invites'] && check_recharge($user_id, $step_no) >= $res_task['no_of_invites']) {

			$sel_task_claim = dbQuery("SELECT * FROM tabl_invitation_task_claim WHERE user_id='$user_id' AND `step_no`='$step_no'");
			if ($res_task_claim = dbFetchAssoc($sel_task_claim)) {
				return 2;
			}

			return 1;
		}
	}

	// Return the total downlines count if the task requirement is not met
	return 0;
}




function check_bet_amount($user_id, $step_no)
{
	$date = date("Y-m-d");

	// Fetch the total betting amount of the current user for the specified step
	$betting_amount = 0;

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_anb_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_aviator_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_wheel_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_wingonew_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}

	// Query to get the total betting amount for the current user and step
	$betting_query = "SELECT SUM(amount) AS total_betting_amount FROM `tabl_wingo_betting` WHERE `user_id` = '$user_id' AND DATE(`date`)='$date'";

	$sel_betting = dbQuery($betting_query);
	if ($res_betting = dbFetchAssoc($sel_betting)) {
		// Extract the total betting amount
		$betting_amount += $res_betting['total_betting_amount'];
	}


	$sel_task = dbQuery("SELECT * FROM tabl_activity_task WHERE `step_no`='$step_no'");
	if ($res_task = dbFetchAssoc($sel_task)) {

		if ($betting_amount >= $res_task['bet_amount']) {
			$betting_amount = $res_task['bet_amount'];
		}
	}


	return $betting_amount;
}



function check_activity_eligibility($user_id, $step_no)
{
	// $date = date("Y-m-d H:i:s");
	$date = date("Y-m-d");

	// echo $date;

	// Fetch the task details
	$sel_task = dbQuery("SELECT * FROM tabl_activity_task WHERE `step_no`='$step_no'");
	if ($res_task = dbFetchAssoc($sel_task)) {
		if (check_bet_amount($user_id, $step_no) >= $res_task['bet_amount']) {

			$sel_task_claim = dbQuery("SELECT * FROM tabl_activity_task_claim WHERE user_id='$user_id' AND `step_no`='$step_no' AND DATE(`date`)='$date'");
			if ($res_task_claim = dbFetchAssoc($sel_task_claim)) {
				return 2;
			}

			return 1;
		}
	}

	// Return the total downlines count if the task requirement is not met
	return 0;
}






function check_exp($user_id)
{
	$sel = dbQuery("SELECT SUM(exp) FROM tabl_experience WHERE user_id='" . $user_id . "'");
	$res = dbFetchAssoc($sel);

	return $res['SUM(exp)'];
}


function check_level($user_id)
{
	$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' ORDER BY id DESC");
	if ($res_user_level = dbFetchAssoc($sel_user_level)) {
		return $res_user_level['vip_level'];
	}

	return false;
}


function upgrade_level($user_id, $level)
{
	$date = date('Y-m-d H:i:s');
	$result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $level . "', date='" . $date . "'");
	if ($result) {
		$order_id = dbInsertId();

		if (dbQuery("UPDATE tabl_user SET vip_level='" . $level . "' WHERE `id` = '" . $user_id . "'")) {

			$sel_level = dbQuery("SELECT * FROM tabl_level WHERE `level`='$level'");
			if ($res_level = dbFetchAssoc($sel_level)) {
				$sel1 = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $user_id . "', order_id='$order_id', amount='" . $res_level['level_up_reward'] . "', type='credit', actiontype='level_up_reward', date='" . $date . "'");

				if ($sel1) {
					update_wallet($user_id, $res_level['level_up_reward'], 'credit');
				}

				return true;
			}
		}
	}

	return false;
}


function upgrade_vip_level1($user_id)
{
	$date = date('Y-m-d H:i:s');
	$experience = check_exp($user_id);

	$sel_level = dbQuery("SELECT * FROM tabl_level WHERE exp_required<='$experience' ORDER BY `level` DESC");
	if ($res_level = dbFetchAssoc($sel_level)) {
		// $result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $res_level['level'] . "', date='" . $date . "'");

		$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' AND vip_level='" . $res_level['level'] . "' ORDER BY id DESC");
		if (!$res_user_level = dbFetchAssoc($sel_user_level)) {
			return upgrade_level($user_id, $res_level['level']);
		}
	}

	return false;
}

function upgrade_vip_level($user_id)
{
	$date = date('Y-m-d H:i:s');
	$experience = check_exp($user_id);

	$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $user_id . "'");
	if ($user = dbFetchAssoc($userq)) {

		$sel_level = dbQuery("SELECT * FROM tabl_level WHERE exp_required<='$experience' AND `level` > '" . $user['vip_level'] . "'  ORDER BY `level`");
		if ($res_level = dbFetchAssoc($sel_level)) {
			// $result = dbQuery("INSERT INTO tabl_my_level SET `user_id`='" . $user_id . "', vip_level='" . $res_level['level'] . "', date='" . $date . "'");

			$sel_user_level = dbQuery("SELECT * FROM tabl_my_level WHERE user_id = '$user_id' AND vip_level='" . $res_level['level'] . "' ORDER BY id DESC");
			if (!$res_user_level = dbFetchAssoc($sel_user_level)) {
				return upgrade_level($user_id, $res_level['level']);
			}
		}
	}

	return false;
}





function betting_rebate($user_id, $total_amount, $game_id, $game, $bet_id)
{
	$date = date('Y-m-d H:i:s');

	$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $user_id . "'");
	if ($user = dbFetchAssoc($userq)) {
		$sel_level = dbQuery("SELECT * FROM tabl_level WHERE `level` = '" . $user['vip_level'] . "'");
		if ($res_level = dbFetchAssoc($sel_level)) {
			$rebate_rate = $res_level['rebate_rate'];
			$rebate = $total_amount * $rebate_rate / 100;

			$result = dbQuery("INSERT INTO `tabl_betting_rebate` SET `user_id`='$user_id', `bet_id`='$bet_id', `game`='$game', `game_id`='$game_id', `bet_amount`='$total_amount', `rebate_rate`='$rebate_rate', `rebate_amount`='$rebate', `date`='$date'");
			@$order_id = dbInsertId();

			if ($rebate) {
				$sel = dbQuery("INSERT INTO tabl_walletsummery SET user_id='" . $user_id . "', order_id='$order_id', amount='$rebate', type='credit', actiontype='betting_rebate', date='" . $date . "'");

				if ($sel) {
					update_wallet($user_id, $rebate, 'credit');
				}
			}
		}
	}
}







function get_wingo_gameid($game_type)
{
	$game_id = '';

	$sel_game = dbQuery("SELECT game_id FROM `tabl_wingonew_gameid` WHERE game_type=$game_type ORDER BY id DESC LIMIT 1");
	// Fetch the result
	if ($res_game = dbFetchAssoc($sel_game)) {
		$game_id = $res_game['game_id'];
	}

	return $game_id;
}


function get_k3_gameid($game_type)
{
	$game_id = '';

	$sel_game = dbQuery("SELECT game_id FROM `tabl_k3_gameid` WHERE game_type=$game_type ORDER BY id DESC LIMIT 1");
	// Fetch the result
	if ($res_game = dbFetchAssoc($sel_game)) {
		$game_id = $res_game['game_id'];
	}

	return $game_id;
}









function wingo_get_bet_amt1($game_id, $game_type, $values, $type)
{

	if ($type == "tradeamount") {
		$result = dbQuery("SELECT SUM(amount)as tradeamount FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `acceptrule`='1'");

		// Loop through each row of the result set
		if ($row = dbFetchAssoc($result)) {
			return $row['tradeamount'];
		}
	} else if ($type == "winamount") {

		$winamount = 0;

		foreach ($values as $value) {
			if ($value == 'green' || $value == 'orange') {
				$multiplier = 2;
				$val = $value;
			} else if ($value == 'green2') {
				$multiplier = 1.5;
				$val = 'green';
			} else if ($value == 'orange2') {
				$multiplier = 1.5;
				$val = 'orange';
			} else if ($value == 'white') {
				$multiplier = 4.5;
				$val = $value;
			} else if ($value == 'big' || $value == 'small') {
				$multiplier = 2;
				$val = $value;
			} else {
				$multiplier = 9;
				$val = $value;

			}
			// Execute the SQL query and fetch the results, assuming $con is the database connection
			$result = dbQuery("SELECT * FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `value`='$val' AND `acceptrule`='1'");

			// Loop through each row of the result set
			while ($row = dbFetchAssoc($result)) {
				$winamount += ($row['amount'] - ($row['amount'] / 100 * 2)) * $multiplier;
			}

		}

		// Return the requested column value
		return $winamount;
	}
}



function wingo_get_bet_amt($game_id, $game_type, $values, $type)
{

	if ($type == "tradeamount") {
		$result = dbQuery("SELECT SUM(amount)as tradeamount FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `acceptrule`='1'");

		// Loop through each row of the result set
		if ($row = dbFetchAssoc($result)) {
			return $row['tradeamount'];
		}
	} else if ($type == "betamount") {

		$betamount = 0;

		foreach ($values as $value) {
			if ($value == 'green' || $value == 'orange') {
				$val = $value;
			} else if ($value == 'green2') {
				$val = 'green';
			} else if ($value == 'orange2') {
				$val = 'orange';
			} else if ($value == 'white') {
				$val = $value;
			} else if ($value == 'big' || $value == 'small') {
				$val = $value;
			} else {
				$val = $value;

			}

			// echo $game_id;
			// echo $game_type;
			// echo $val;

			// Execute the SQL query and fetch the results, assuming $con is the database connection
			$result = dbQuery("SELECT * FROM `tabl_wingonew_betting` WHERE `game_id`='$game_id' AND `game_type`='$game_type' AND `value`='$val' AND `acceptrule`='1'");

			// Loop through each row of the result set
			while ($row = dbFetchAssoc($result)) {
				$betamount += $row['amount'];
			}

		}

		// Return the requested column value
		return $betamount;
	}
}



function wingo_bet_amount($game_id, $game_type, $x)
{
	if ($x == 1 || $x == 3 || $x == 7 || $x == 9) {//======for green
		$color = 'green';
		$greentotal = wingo_get_bet_amt($game_id, $game_type, ['green'], 'betamount');


		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $greentotal + $big_small + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');

	} else if ($x == 2 || $x == 4 || $x == 6 || $x == 8) {
		$color = 'orange';
		$orangetotal = wingo_get_bet_amt($game_id, $game_type, ['orange'], 'betamount');

		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $orangetotal + $big_small + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');

	} else if ($x == 0) {
		$color = 'orange+white';
		// $orangetotal = wingo_winner($game_id, $game_type, ['orange', 'white2'], 'betamount');
		$orangetotal = wingo_get_bet_amt($game_id, $game_type, ['orange2'], 'betamount');
		$vtotal = wingo_get_bet_amt($game_id, $game_type, ['white'], 'betamount');

		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $orangetotal + $big_small + $vtotal + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');

	} else if ($x == 5) {
		$color = 'green+white';
		// $orangetotal = wingo_winner($game_id, $game_type, ['green', 'white2'], 'betamount');
		$orangetotal = wingo_get_bet_amt($game_id, $game_type, ['green2'], 'betamount');
		$vtotal = wingo_get_bet_amt($game_id, $game_type, ['white'], 'betamount');

		$num_type = $x < 5 ? "small" : "big";
		$big_small = wingo_get_bet_amt($game_id, $game_type, [$num_type], 'betamount');

		$total = $orangetotal + $big_small + $vtotal + wingo_get_bet_amt($game_id, $game_type, [$x], 'betamount');
	}

	return $total;

}




function getTotalFirstRechargeAmount($ownCode)
{
	// Initialize total first recharge amount
	$totalFirstRechargeAmount = 0;

	// Query to select the minimum deposit date and amount for each user's first deposit
	$firstDepositQuery = dbQuery("SELECT `user_id`, MIN(`date`) AS first_deposit_date, SUM(`amount`) AS first_deposit_amount 
        FROM `tabl_deposits` 
        WHERE `user_id` IN (SELECT `id` FROM `tabl_user` WHERE `ref_code` = '$ownCode') AND `status`='1'
        GROUP BY `user_id`
    ");

	// Iterate through each user's first deposit dates and amounts
	while ($firstDepositResult = dbFetchAssoc($firstDepositQuery)) {
		// Add the deposit amount to the total first recharge amount
		$totalFirstRechargeAmount += $firstDepositResult['first_deposit_amount'];
	}

	// Query to select direct downlines
	$directDownlinesQuery = dbQuery("SELECT own_code FROM tabl_user WHERE ref_code = '$ownCode'");

	// Recursively calculate total first recharge amount for each direct downline
	while ($directDownline = dbFetchAssoc($directDownlinesQuery)) {
		$totalFirstRechargeAmount += getTotalFirstRechargeAmount($directDownline['own_code']);
	}

	return $totalFirstRechargeAmount;
}



function getTotalWinnings($user_id)
{
	// Fetch the total win amount of the current user for the specified step
	$win_amount = 0;

	// Query to get the total win amount for the current user and step
	$win_query = "SELECT SUM(amount) AS total_win_amount FROM `tabl_walletsummery` WHERE `user_id` = '$user_id' AND `actiontype`='win'";

	$sel_win = dbQuery($win_query);
	if ($res_win = dbFetchAssoc($sel_win)) {
		// Extract the total win amount
		$win_amount += $res_win['total_win_amount'];
	}

	// return number_format($win_amount, 2);
	return $win_amount;
}

function getTotalWithdrawal($user_id)
{
	// Fetch the total win amount of the current user for the specified step
	$withdrawal_amount = 0;

	// Query to get the total win amount for the current user and step
	$withdrawal_query = "SELECT SUM(amount) AS total_withdrawal FROM `tabl_walletsummery` WHERE `user_id` = '$user_id' AND `actiontype` LIKE 'withdraw%'";

	$sel_withdrawal = dbQuery($withdrawal_query);
	if ($res_withdrawal = dbFetchAssoc($sel_withdrawal)) {
		// Extract the total win amount
		$withdrawal_amount += $res_withdrawal['total_withdrawal'];
	}

	// return number_format($withdrawal_amount, 2);
	return $withdrawal_amount;
}


function getWithdrawalLimit($user_id)
{
	$userq = dbQuery("SELECT * FROM tabl_user WHERE id='" . $_SESSION['user_id'] . "'");
	if (!$user = dbFetchAssoc($userq)) {
		echo "<script>window.location.href='./login.php';</script>";
	}

	$totalFirstRecharge = getTotalFirstRechargeAmount($user['own_code']);
	$FirstRecharge_limit = $totalFirstRecharge * 0.9;

	$totalWinnings = getTotalWinnings($user_id) >= 1000 ? getTotalWinnings($user_id) : 0;

	$total_widrawal_limit = $FirstRecharge_limit + $totalWinnings;

	$final_limit = $total_widrawal_limit - getTotalWithdrawal($user_id);
	// return $final_limit > 0 ? number_format($final_limit, 2) : 0;

	return $final_limit > 0 ? $final_limit : 0;
}



$game_max_loss = 0;
$game_setting = 0;

$sel_gamesetting = dbQuery("SELECT * FROM tabl_gamesettings WHERE `game`='aviator'");
if ($res_gamesetting = dbFetchAssoc($sel_gamesetting)) {
	$game_max_loss = $res_gamesetting['max_loss'];
	$game_setting = $res_gamesetting['settingtype'];
}


// $base_url = "http://*********/showbaazi/";
// $base_url = "https://showbaazi.com/";

$base_url = "https://bigwinnersclub.in/showbaazi/";

