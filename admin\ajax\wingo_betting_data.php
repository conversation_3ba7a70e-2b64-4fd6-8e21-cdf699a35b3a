<?php
session_start();
include ('../lib/db_connection.php');

$game_type = 1;
$game_id = get_wingo_gameid($game_type);

if (isset($_REQUEST['game_type']) && $_REQUEST['game_type'] != '') {
    $game_type = $_REQUEST['game_type'];
}

if (isset($_REQUEST['game_id']) && $_REQUEST['game_id'] != '') {
    $game_id = $_REQUEST['game_id'];
}

// echo $game_type;
// echo $game_id;


// $game_values = array(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 'green', 'white', 'orange', 'big', 'small');

// $game_values = array(0, 1, 2, 3, 4, 5, 6, 7, 8, 9);

// foreach ($game_values as $game_value) {


$manual_game = 'wingo_' . $game_type;

$switchvalue = '';

$chkmanualswitchQuery = dbQuery("SELECT * FROM `tabl_manualresultswitch` WHERE `game`='$manual_game'");
if ($switchResult = dbFetchAssoc($chkmanualswitchQuery)) {
    $switchstatus = $switchResult['switch'];
    if ($switchstatus == 'yes') {
        $switchvalue = $switchResult['value'];
    }
}


$manualresultQuery = dbQuery("SELECT `id`,`value`, color,`number`, num_type FROM `tabl_wingonew_manualresult`");

while ($manualResult = dbFetchAssoc($manualresultQuery)) {
    $manualid = $manualResult['id'];
    $manualcolor = $manualResult['color'];
    $game_value = $manualResult['number'];
    $manual_num_type = $manualResult['num_type'];


    $checked = $switchvalue == $game_value ? "checked" : "";
    ?>
    <tr>
        <td>
            <?php echo $game_id; ?>
        </td>
        <td>
            <?php echo $game_value . ' - ' . ucfirst($manual_num_type) . ' - ' . $manualcolor; ?>
        </td>
        <td>
            <?php echo wingo_bet_amount($game_id, $game_type, $game_value); ?>
        </td>
        <td>
            <input type="radio" name="choose_result" value="<?php echo $game_value; ?>" id="num_<?php echo $game_value; ?>"
                onchange="save_result(<?php echo $game_id; ?>, <?php echo $game_type; ?>, <?php echo $game_value; ?>);"
                <?php echo $checked; ?>>
            <label for="num_<?php echo $game_value; ?>"> Choose
                <?php echo $game_value; ?>
            </label>
        </td>
    </tr>
    <?php
}

?>